E:\OSprog\obj\Debug\net6.0-windows\CustomerOrderManager.csproj.AssemblyReference.cache
E:\OSprog\obj\Debug\net6.0-windows\CustomerOrderManager.GeneratedMSBuildEditorConfig.editorconfig
E:\OSprog\obj\Debug\net6.0-windows\CustomerOrderManager.AssemblyInfoInputs.cache
E:\OSprog\obj\Debug\net6.0-windows\CustomerOrderManager.AssemblyInfo.cs
E:\OSprog\obj\Debug\net6.0-windows\CustomerOrderManager.csproj.CoreCompileInputs.cache
E:\OSprog\bin\Debug\net6.0-windows\CustomerOrderManager.exe
E:\OSprog\bin\Debug\net6.0-windows\CustomerOrderManager.deps.json
E:\OSprog\bin\Debug\net6.0-windows\CustomerOrderManager.runtimeconfig.json
E:\OSprog\bin\Debug\net6.0-windows\CustomerOrderManager.dll
E:\OSprog\bin\Debug\net6.0-windows\CustomerOrderManager.pdb
E:\OSprog\bin\Debug\net6.0-windows\EntityFramework.SqlServer.dll
E:\OSprog\bin\Debug\net6.0-windows\EntityFramework.dll
E:\OSprog\bin\Debug\net6.0-windows\EPPlus.dll
E:\OSprog\bin\Debug\net6.0-windows\EPPlus.Interfaces.dll
E:\OSprog\bin\Debug\net6.0-windows\EPPlus.System.Drawing.dll
E:\OSprog\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.dll
E:\OSprog\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.Abstractions.dll
E:\OSprog\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.FileExtensions.dll
E:\OSprog\bin\Debug\net6.0-windows\Microsoft.Extensions.Configuration.Json.dll
E:\OSprog\bin\Debug\net6.0-windows\Microsoft.Extensions.FileProviders.Abstractions.dll
E:\OSprog\bin\Debug\net6.0-windows\Microsoft.Extensions.FileProviders.Physical.dll
E:\OSprog\bin\Debug\net6.0-windows\Microsoft.Extensions.FileSystemGlobbing.dll
E:\OSprog\bin\Debug\net6.0-windows\Microsoft.Extensions.Primitives.dll
E:\OSprog\bin\Debug\net6.0-windows\Microsoft.IO.RecyclableMemoryStream.dll
E:\OSprog\bin\Debug\net6.0-windows\System.Data.SQLite.dll
E:\OSprog\bin\Debug\net6.0-windows\System.Data.SqlClient.dll
E:\OSprog\bin\Debug\net6.0-windows\System.Data.SQLite.EF6.dll
E:\OSprog\bin\Debug\net6.0-windows\System.Security.Cryptography.Pkcs.dll
E:\OSprog\bin\Debug\net6.0-windows\runtimes\win-arm64\native\sni.dll
E:\OSprog\bin\Debug\net6.0-windows\runtimes\win-x64\native\sni.dll
E:\OSprog\bin\Debug\net6.0-windows\runtimes\win-x86\native\sni.dll
E:\OSprog\bin\Debug\net6.0-windows\runtimes\linux-x64\native\SQLite.Interop.dll
E:\OSprog\bin\Debug\net6.0-windows\runtimes\osx-x64\native\SQLite.Interop.dll
E:\OSprog\bin\Debug\net6.0-windows\runtimes\win-x64\native\SQLite.Interop.dll
E:\OSprog\bin\Debug\net6.0-windows\runtimes\win-x86\native\SQLite.Interop.dll
E:\OSprog\bin\Debug\net6.0-windows\runtimes\unix\lib\netcoreapp2.1\System.Data.SqlClient.dll
E:\OSprog\bin\Debug\net6.0-windows\runtimes\win\lib\netcoreapp2.1\System.Data.SqlClient.dll
E:\OSprog\bin\Debug\net6.0-windows\runtimes\win\lib\net6.0\System.Security.Cryptography.Pkcs.dll
E:\OSprog\obj\Debug\net6.0-windows\Customer.E4FE7017.Up2Date
E:\OSprog\obj\Debug\net6.0-windows\CustomerOrderManager.dll
E:\OSprog\obj\Debug\net6.0-windows\refint\CustomerOrderManager.dll
E:\OSprog\obj\Debug\net6.0-windows\CustomerOrderManager.pdb
E:\OSprog\obj\Debug\net6.0-windows\CustomerOrderManager.genruntimeconfig.cache
E:\OSprog\obj\Debug\net6.0-windows\ref\CustomerOrderManager.dll
