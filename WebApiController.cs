using System;
using System.Collections.Generic;
using System.Data;
using System.Text.Json;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace CustomerOrderManager
{
    public class WebApiController
    {
        private DatabaseManager dbManager;
        private HttpListener listener;
        private bool isRunning = false;

        public WebApiController()
        {
            dbManager = new DatabaseManager();
            listener = new HttpListener();
            // إضافة البادئات للاستماع
            listener.Prefixes.Add("http://localhost:8080/");
            listener.Prefixes.Add("http://127.0.0.1:8080/");
        }

        public async Task StartAsync()
        {
            if (!isRunning)
            {
                listener.Start();
                isRunning = true;
                Console.WriteLine("🌐 خادم الويب يعمل على: http://localhost:8080");
                Console.WriteLine("📊 API متاح على: http://localhost:8080/api/");
                
                while (isRunning)
                {
                    try
                    {
                        var context = await listener.GetContextAsync();
                        _ = Task.Run(() => ProcessRequest(context));
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"خطأ في الخادم: {ex.Message}");
                    }
                }
            }
        }

        public void Stop()
        {
            isRunning = false;
            listener?.Stop();
            listener?.Close();
        }

        private async Task ProcessRequest(HttpListenerContext context)
        {
            var request = context.Request;
            var response = context.Response;

            // إضافة CORS headers
            response.Headers.Add("Access-Control-Allow-Origin", "*");
            response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
            response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");

            try
            {
                if (request.HttpMethod == "OPTIONS")
                {
                    response.StatusCode = 200;
                    response.Close();
                    return;
                }

                string path = request.Url.AbsolutePath.ToLower();
                string method = request.HttpMethod;

                string responseText = "";

                if (path.StartsWith("/api/commandes"))
                {
                    responseText = await HandleCommandesApi(method, request);
                }
                else if (path.StartsWith("/api/statuts"))
                {
                    responseText = await HandleStatutsApi(method, request);
                }
                else if (path == "/" || path.EndsWith(".html"))
                {
                    await ServeStaticFile(context, path);
                    return;
                }
                else
                {
                    response.StatusCode = 404;
                    responseText = JsonSerializer.Serialize(new { error = "المسار غير موجود" });
                }

                byte[] buffer = Encoding.UTF8.GetBytes(responseText);
                response.ContentType = "application/json; charset=utf-8";
                response.ContentLength64 = buffer.Length;
                await response.OutputStream.WriteAsync(buffer, 0, buffer.Length);
            }
            catch (Exception ex)
            {
                response.StatusCode = 500;
                string errorResponse = JsonSerializer.Serialize(new { error = ex.Message });
                byte[] errorBuffer = Encoding.UTF8.GetBytes(errorResponse);
                await response.OutputStream.WriteAsync(errorBuffer, 0, errorBuffer.Length);
            }
            finally
            {
                response.Close();
            }
        }

        private async Task<string> HandleCommandesApi(string method, HttpListenerRequest request)
        {
            switch (method)
            {
                case "GET":
                    return GetAllCommandes();
                
                case "POST":
                    return await AddCommande(request);
                
                case "PUT":
                    return await UpdateCommande(request);
                
                case "DELETE":
                    return await DeleteCommande(request);
                
                default:
                    throw new NotSupportedException($"الطريقة {method} غير مدعومة");
            }
        }

        private string GetAllCommandes()
        {
            var dataTable = dbManager.GetToutesCommandes();
            var commandes = new List<object>();

            foreach (DataRow row in dataTable.Rows)
            {
                var statuts = new List<int>();
                for (int i = 1; i <= 20; i++) // دعم حتى 20 عمود
                {
                    var columnName = $"Statut{i}";
                    if (dataTable.Columns.Contains(columnName))
                    {
                        statuts.Add(Convert.ToInt32(row[columnName] ?? 0));
                    }
                    else
                    {
                        statuts.Add(0);
                    }
                }

                commandes.Add(new
                {
                    id = Convert.ToInt32(row["Id"]),
                    refClient = row["RefClient"].ToString(),
                    nomPrenom = row["NomPrenom"].ToString(),
                    articles = row["Articles"].ToString(),
                    dateCommande = row["DateCommande"].ToString(),
                    prix = Convert.ToDouble(row["Prix"]),
                    totalPaye = Convert.ToDouble(row["TotalPaye"]),
                    statuts = statuts,
                    nombreColonnes = Convert.ToInt32(row["NombreColonnes"] ?? 10)
                });
            }

            return JsonSerializer.Serialize(new { success = true, data = commandes });
        }

        private async Task<string> AddCommande(HttpListenerRequest request)
        {
            using var reader = new System.IO.StreamReader(request.InputStream);
            string body = await reader.ReadToEndAsync();
            
            var commandeData = JsonSerializer.Deserialize<JsonElement>(body);
            
            string refClient = commandeData.GetProperty("refClient").GetString();
            string nomPrenom = commandeData.GetProperty("nomPrenom").GetString();
            string articles = commandeData.GetProperty("articles").GetString();
            string dateCommande = commandeData.GetProperty("dateCommande").GetString();
            decimal prix = (decimal)commandeData.GetProperty("prix").GetDouble();
            decimal totalPaye = (decimal)commandeData.GetProperty("totalPaye").GetDouble();

            dbManager.AjouterCommande(refClient, nomPrenom, articles, dateCommande, prix, totalPaye);
            
            return JsonSerializer.Serialize(new { success = true, message = "تم إضافة الطلب بنجاح" });
        }

        private async Task<string> UpdateCommande(HttpListenerRequest request)
        {
            using var reader = new System.IO.StreamReader(request.InputStream);
            string body = await reader.ReadToEndAsync();
            
            var updateData = JsonSerializer.Deserialize<JsonElement>(body);
            
            int id = updateData.GetProperty("id").GetInt32();
            // تحديث الطلب في قاعدة البيانات
            // سيتم تنفيذ هذا في DatabaseManager
            
            return JsonSerializer.Serialize(new { success = true, message = "تم تحديث الطلب بنجاح" });
        }

        private async Task<string> DeleteCommande(HttpListenerRequest request)
        {
            string query = request.Url.Query;
            if (query.StartsWith("?id="))
            {
                int id = int.Parse(query.Substring(4));
                dbManager.SupprimerCommande(id);
                return JsonSerializer.Serialize(new { success = true, message = "تم حذف الطلب بنجاح" });
            }
            
            throw new ArgumentException("معرف الطلب مطلوب");
        }

        private async Task<string> HandleStatutsApi(string method, HttpListenerRequest request)
        {
            if (method == "PUT")
            {
                using var reader = new System.IO.StreamReader(request.InputStream);
                string body = await reader.ReadToEndAsync();
                
                var statutData = JsonSerializer.Deserialize<JsonElement>(body);
                
                int id = statutData.GetProperty("id").GetInt32();
                int index = statutData.GetProperty("index").GetInt32();
                bool value = statutData.GetProperty("value").GetBoolean();
                
                dbManager.MettreAJourStatut(id, index + 1, value ? 1 : 0);
                
                return JsonSerializer.Serialize(new { success = true, message = "تم تحديث الحالة بنجاح" });
            }
            
            throw new NotSupportedException($"الطريقة {method} غير مدعومة للحالات");
        }

        private async Task ServeStaticFile(HttpListenerContext context, string path)
        {
            if (path == "/")
                path = "/GestionnaireGroupes.html";
            
            string filePath = "." + path;
            
            if (System.IO.File.Exists(filePath))
            {
                string content = await System.IO.File.ReadAllTextAsync(filePath);
                byte[] buffer = Encoding.UTF8.GetBytes(content);
                
                context.Response.ContentType = GetContentType(path);
                context.Response.ContentLength64 = buffer.Length;
                await context.Response.OutputStream.WriteAsync(buffer, 0, buffer.Length);
            }
            else
            {
                context.Response.StatusCode = 404;
                string notFound = "الملف غير موجود";
                byte[] buffer = Encoding.UTF8.GetBytes(notFound);
                await context.Response.OutputStream.WriteAsync(buffer, 0, buffer.Length);
            }
        }

        private string GetContentType(string path)
        {
            string extension = System.IO.Path.GetExtension(path).ToLower();
            return extension switch
            {
                ".html" => "text/html; charset=utf-8",
                ".css" => "text/css",
                ".js" => "application/javascript",
                ".json" => "application/json",
                _ => "text/plain"
            };
        }
    }
}
