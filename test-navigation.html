<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Navigation</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .test-button {
            display: block;
            margin: 10px 0;
            padding: 15px 30px;
            background: linear-gradient(135deg, #4472C4, #5B9BD5);
            color: white;
            text-decoration: none;
            border-radius: 5px;
            text-align: center;
            font-size: 16px;
            transition: all 0.3s;
        }
        .test-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .info {
            background: white;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            border-left: 4px solid #4472C4;
        }
    </style>
</head>
<body>
    <h1>🧪 اختبار التنقل</h1>
    
    <div class="info">
        <h3>اختبار الروابط:</h3>
        <p>انقر على الأزرار أدناه لاختبار التنقل بين الصفحات</p>
    </div>

    <a href="GestionnaireGroupes.html" class="test-button">
        📋 فتح صفحة المجموعات
    </a>

    <a href="GestionnaireGROUPE 02.html" class="test-button">
        🛒 فتح GROUPE 02 مباشرة
    </a>

    <a href="GestionnaireCommandes.html" class="test-button">
        📊 فتح صفحة الطلبات العامة
    </a>

    <div class="info">
        <h3>معلومات الملفات:</h3>
        <ul>
            <li><strong>GestionnaireGroupes.html</strong> - الصفحة الرئيسية للمجموعات</li>
            <li><strong>GestionnaireGROUPE 02.html</strong> - صفحة إدارة طلبات المجموعة 02</li>
            <li><strong>GestionnaireCommandes.html</strong> - صفحة الطلبات العامة</li>
        </ul>
    </div>

    <script>
        // اختبار وجود الملفات
        function testFileExists(filename) {
            fetch(filename)
                .then(response => {
                    if (response.ok) {
                        console.log(`✅ ${filename} موجود`);
                    } else {
                        console.log(`❌ ${filename} غير موجود`);
                    }
                })
                .catch(error => {
                    console.log(`❌ خطأ في الوصول إلى ${filename}:`, error);
                });
        }

        // اختبار الملفات عند تحميل الصفحة
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 اختبار وجود الملفات...');
            testFileExists('GestionnaireGroupes.html');
            testFileExists('GestionnaireGROUPE 02.html');
            testFileExists('GestionnaireCommandes.html');
        });
    </script>
</body>
</html>
