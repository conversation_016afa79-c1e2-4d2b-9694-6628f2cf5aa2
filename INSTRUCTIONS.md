# 📋 Instructions d'Installation et d'Utilisation

## 🚀 Installation Rapide

### Étape 1: Installer .NET 6.0
1. **Double-cliquez** sur `installer_dotnet.bat`
2. **Suivez** le lien qui s'affiche pour télécharger .NET 6.0
3. **Installez** .NET 6.0 Runtime et SDK

### Étape 2: Compiler l'Application
1. **Double-cliquez** sur `compiler.bat`
2. **Attendez** la fin de la compilation
3. Un fichier `.exe` sera créé

### Étape 3: Lancer l'Application
1. **Double-cliquez** sur `executer.bat`
2. L'application se lance automatiquement

---

## 🎯 Utilisation de l'Application

### Interface Principale
- **Titre**: "GESTIONNAIRE DE COMMANDES CLIENTS - GROUPE 02"
- **Tableau**: Affiche toutes les commandes
- **Boutons**: <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Actualiser

### Fonctionnalités

#### ➕ Ajouter une Commande
1. Cliquez sur **"Ajouter Commande"**
2. Remplissez le formulaire:
   - **Référence Client**: Ex: OS24S1793
   - **Nom et Prénom**: Ex: BEN SEGHIR ABDERAHMANE
   - **Articles**: Description des produits
   - **Date de Commande**: Sélectionnez la date
   - **Prix**: Montant total
   - **Total Payé**: Montant déjà payé
3. Cliquez sur **"Sauvegarder"**

#### ❌ Supprimer une Commande
1. **Sélectionnez** une ligne dans le tableau
2. Cliquez sur **"Supprimer"**
3. **Confirmez** la suppression

#### 🔄 Actualiser les Données
- Cliquez sur **"Actualiser"** pour recharger les données

### Colonnes de Suivi (1-10)
- Utilisez les colonnes numérotées 1 à 10 pour suivre l'état des commandes
- Cochez/décochez selon l'avancement

---

## 📊 Données Incluses

L'application contient déjà 10 commandes d'exemple basées sur votre tableau:

1. **OS24S1793** - BEN SEGHIR ABDERAHMANE - TELE REALME C65
2. **OS24S1796** - SIGA MOHAMMED - REF 580L+ TV 40"
3. **OS24S1805** - CHABIRA IBRAHIM - MEUBLES
4. **OS24S1807** - BEKKAYE ABDELBAKI - RADIATEUR A GAZ
5. **OS24S1814** - MAZHOUD KHAIRA - M.LAVER + TV 43"
6. **OS24S1810** - KOURRINI ATTIA - CUISINIER 4 FEUX
7. **OS24S1811** - DJAMED ELHOUCINE - TV 43" GOOGLE TV
8. **OS24S1822** - DJIREB AMINA - CHAUF-EAU + RADIATEUR
9. **OS24S1830** - HAMDANI HICHAM - RADIATEUR GAZ + TENDEUSE
10. **OS24S1832** - DZIRI MOHAMED - MICRO ONDES 23 L

---

## 🗂️ Structure des Fichiers

```
CustomerOrderManager/
├── 📄 CustomerOrderManager.csproj  # Configuration du projet
├── 📄 Program.cs                   # Point d'entrée
├── 📄 MainForm.cs                  # Interface principale
├── 📄 AjouterCommandeForm.cs       # Formulaire d'ajout
├── 📄 DatabaseManager.cs           # Gestion base de données
├── 📄 installer_dotnet.bat         # Installation .NET
├── 📄 compiler.bat                 # Compilation
├── 📄 executer.bat                 # Lancement
├── 📄 README.md                    # Documentation
└── 📄 INSTRUCTIONS.md              # Ce fichier
```

---

## 🔧 Dépannage

### Problème: "dotnet n'est pas reconnu"
**Solution**: Installez .NET 6.0 avec `installer_dotnet.bat`

### Problème: Erreur de compilation
**Solution**: Vérifiez que .NET SDK est installé (pas seulement Runtime)

### Problème: L'application ne se lance pas
**Solution**: 
1. Recompilez avec `compiler.bat`
2. Vérifiez les permissions Windows

### Problème: Base de données corrompue
**Solution**: Supprimez le fichier `orders.db` et relancez l'application

---

## 📞 Support

Pour toute question ou problème:
1. Vérifiez ce fichier d'instructions
2. Consultez le fichier README.md
3. Contactez l'équipe de développement

---

**🏢 GROUPE 02** - Gestionnaire de Commandes Clients  
*Application Windows Forms en C# avec interface classique française*
