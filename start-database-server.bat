@echo off
echo ========================================
echo 🗄️ خادم قاعدة البيانات - مدير الطلبات
echo GROUPE 02
echo ========================================
echo.

cd WebServer

echo 🔧 بناء الخادم...
dotnet build --configuration Release

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء الخادم
    pause
    exit /b 1
)

echo.
echo 🚀 بدء تشغيل خادم الويب مع قاعدة البيانات...
echo 🌐 سيكون متاحاً على: http://localhost:8080
echo 📊 واجهة الإدارة: http://localhost:8080/GestionnaireGROUPE02-Database.html
echo 🗄️ البيانات: محفوظة في الذاكرة (تجريبي)
echo.
echo اضغط Ctrl+C لإيقاف الخادم
echo.

dotnet run --configuration Release

cd ..
pause
