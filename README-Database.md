# 🗄️ نظام إدارة الطلبات مع قاعدة البيانات

## 📋 نظرة عامة

تم تطوير النظام ليدعم قاعدة بيانات SQLite حقيقية بدلاً من التخزين المحلي في المتصفح. هذا يوفر:

- ✅ **بيانات دائمة ومحفوظة**
- ✅ **أداء أفضل مع البيانات الكبيرة**
- ✅ **إمكانية الوصول من أجهزة متعددة**
- ✅ **نسخ احتياطية سهلة**
- ✅ **تحكم فردي في عدد الأعمدة لكل طلب**

## 🚀 كيفية التشغيل

### الطريقة الأولى: تشغيل خادم الويب
```bash
# تشغيل الخادم
start-web-server.bat

# أو باستخدام سطر الأوامر
dotnet run -- --web
```

### الطريقة الثانية: تطبيق Windows Forms التقليدي
```bash
# تشغيل التطبيق التقليدي
executer.bat

# أو باستخدام سطر الأوامر
dotnet run
```

## 🌐 الوصول للنظام

بعد تشغيل خادم الويب:

- **الصفحة الرئيسية**: http://localhost:8080
- **إدارة المجموعات**: http://localhost:8080/GestionnaireGroupes.html
- **إدارة الطلبات (قاعدة البيانات)**: http://localhost:8080/GestionnaireGROUPE02-Database.html
- **API**: http://localhost:8080/api/

## 📊 API المتاحة

### الطلبات (Commandes)
- `GET /api/commandes` - جلب جميع الطلبات
- `POST /api/commandes` - إضافة طلب جديد
- `PUT /api/commandes` - تحديث طلب موجود
- `DELETE /api/commandes?id={id}` - حذف طلب

### الحالات (Statuts)
- `PUT /api/statuts` - تحديث حالة طلب

## 🗃️ قاعدة البيانات

### الملف
- **المسار**: `orders.db` (في مجلد التطبيق)
- **النوع**: SQLite
- **الترميز**: UTF-8

### جدول Commandes
```sql
CREATE TABLE Commandes (
    Id INTEGER PRIMARY KEY AUTOINCREMENT,
    RefClient TEXT NOT NULL,
    NomPrenom TEXT NOT NULL,
    Articles TEXT NOT NULL,
    DateCommande TEXT NOT NULL,
    Prix REAL NOT NULL,
    TotalPaye REAL NOT NULL,
    NombreColonnes INTEGER DEFAULT 10,
    Statut1-20 INTEGER DEFAULT 0,
    DateCreation TEXT DEFAULT CURRENT_TIMESTAMP
);
```

## ✨ المميزات الجديدة

### 🎛️ التحكم الفردي في الأعمدة
- كل طلب له عدد أعمدة منفصل (1-20)
- تحديث فوري للحسابات
- حفظ تلقائي في قاعدة البيانات

### 🔄 التحديث المباشر
- جميع التغييرات تُحفظ فوراً في قاعدة البيانات
- تحديث تلقائي للواجهة
- رسائل تأكيد للعمليات

### 📱 واجهة محسنة
- مؤشر الاتصال بقاعدة البيانات
- رسائل الحالة والأخطاء
- تحميل تدريجي للبيانات

## 🔧 استكشاف الأخطاء

### مشاكل الاتصال
```
❌ خطأ في الاتصال: fetch failed
```
**الحل**: تأكد من تشغيل خادم الويب على المنفذ 8080

### مشاكل قاعدة البيانات
```
❌ فشل في تحميل البيانات
```
**الحل**: تحقق من وجود ملف `orders.db` وصلاحيات الكتابة

### مشاكل البناء
```
❌ فشل في بناء التطبيق
```
**الحل**: تأكد من تثبيت .NET 6.0 أو أحدث

## 📁 هيكل الملفات

```
OSprog/
├── orders.db                          # قاعدة البيانات
├── start-web-server.bat              # تشغيل خادم الويب
├── GestionnaireGROUPE02-Database.html # واجهة قاعدة البيانات
├── WebApiController.cs               # خادم API
├── DatabaseManager.cs                # إدارة قاعدة البيانات
├── Program.cs                         # نقطة البداية
└── README-Database.md                 # هذا الملف
```

## 🔄 الترقية من النظام القديم

1. **نسخ احتياطية**: احفظ بيانات localStorage إذا كانت مهمة
2. **تشغيل النظام الجديد**: استخدم `start-web-server.bat`
3. **إدخال البيانات**: أضف الطلبات يدوياً أو استورد من Excel
4. **اختبار**: تأكد من عمل جميع الوظائف

## 📞 الدعم

في حالة وجود مشاكل:
1. تحقق من ملف `orders.db`
2. راجع رسائل الخطأ في المتصفح (F12)
3. تأكد من تشغيل الخادم بشكل صحيح
4. أعد تشغيل النظام إذا لزم الأمر

---

**🎉 استمتع بالنظام الجديد مع قاعدة البيانات المتقدمة!**
