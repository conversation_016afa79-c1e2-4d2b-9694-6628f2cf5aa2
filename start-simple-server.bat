@echo off
echo ========================================
echo خادم الويب البسيط - مدير الطلبات
echo GROUPE 02
echo ========================================
echo.

echo 🔧 بناء الخادم...
dotnet build SimpleWebServer.csproj --configuration Release

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء الخادم
    pause
    exit /b 1
)

echo.
echo 🚀 بدء تشغيل خادم الويب...
echo 🌐 سيكون متاحاً على: http://localhost:8080
echo 📊 واجهة الإدارة: http://localhost:8080/GestionnaireGROUPE02-Database.html
echo.
echo اضغط Ctrl+C لإيقاف الخادم
echo.

dotnet run --project SimpleWebServer.csproj --configuration Release

pause
