using System;
using System.Drawing;
using System.Windows.Forms;

namespace CustomerOrderManager
{
    public partial class AjouterCommandeForm : Form
    {
        private DatabaseManager dbManager;
        private TextBox txtRefClient;
        private TextBox txtNomPrenom;
        private TextBox txtArticles;
        private DateTimePicker dtpDateCommande;
        private NumericUpDown nudPrix;
        private NumericUpDown nudTotalPaye;
        private Button btnSauvegarder;
        private Button btnAnnuler;
        private Label lblTitre;

        public AjouterCommandeForm()
        {
            InitializeComponent();
            dbManager = new DatabaseManager();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Configuration de la fenêtre
            this.Text = "Ajouter une Nouvelle Commande";
            this.Size = new Size(500, 400);
            this.StartPosition = FormStartPosition.CenterParent;
            this.FormBorderStyle = FormBorderStyle.FixedDialog;
            this.MaximizeBox = false;
            this.MinimizeBox = false;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.Font = new Font("Segoe UI", 9F);

            // Titre
            lblTitre = new Label();
            lblTitre.Text = "NOUVELLE COMMANDE CLIENT";
            lblTitre.Font = new Font("Arial", 12F, FontStyle.Bold);
            lblTitre.ForeColor = Color.FromArgb(0, 51, 102);
            lblTitre.TextAlign = ContentAlignment.MiddleCenter;
            lblTitre.Location = new Point(20, 20);
            lblTitre.Size = new Size(440, 30);
            this.Controls.Add(lblTitre);

            // Référence Client
            Label lblRefClient = new Label();
            lblRefClient.Text = "Référence Client:";
            lblRefClient.Location = new Point(30, 70);
            lblRefClient.Size = new Size(120, 20);
            lblRefClient.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.Controls.Add(lblRefClient);

            txtRefClient = new TextBox();
            txtRefClient.Location = new Point(160, 68);
            txtRefClient.Size = new Size(280, 23);
            txtRefClient.Font = new Font("Segoe UI", 9F);
            this.Controls.Add(txtRefClient);

            // Nom et Prénom
            Label lblNomPrenom = new Label();
            lblNomPrenom.Text = "Nom et Prénom:";
            lblNomPrenom.Location = new Point(30, 105);
            lblNomPrenom.Size = new Size(120, 20);
            lblNomPrenom.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.Controls.Add(lblNomPrenom);

            txtNomPrenom = new TextBox();
            txtNomPrenom.Location = new Point(160, 103);
            txtNomPrenom.Size = new Size(280, 23);
            txtNomPrenom.Font = new Font("Segoe UI", 9F);
            this.Controls.Add(txtNomPrenom);

            // Articles
            Label lblArticles = new Label();
            lblArticles.Text = "Articles:";
            lblArticles.Location = new Point(30, 140);
            lblArticles.Size = new Size(120, 20);
            lblArticles.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.Controls.Add(lblArticles);

            txtArticles = new TextBox();
            txtArticles.Location = new Point(160, 138);
            txtArticles.Size = new Size(280, 60);
            txtArticles.Multiline = true;
            txtArticles.ScrollBars = ScrollBars.Vertical;
            txtArticles.Font = new Font("Segoe UI", 9F);
            this.Controls.Add(txtArticles);

            // Date de Commande
            Label lblDateCommande = new Label();
            lblDateCommande.Text = "Date de Commande:";
            lblDateCommande.Location = new Point(30, 215);
            lblDateCommande.Size = new Size(120, 20);
            lblDateCommande.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.Controls.Add(lblDateCommande);

            dtpDateCommande = new DateTimePicker();
            dtpDateCommande.Location = new Point(160, 213);
            dtpDateCommande.Size = new Size(280, 23);
            dtpDateCommande.Format = DateTimePickerFormat.Short;
            dtpDateCommande.Font = new Font("Segoe UI", 9F);
            this.Controls.Add(dtpDateCommande);

            // Prix
            Label lblPrix = new Label();
            lblPrix.Text = "Prix:";
            lblPrix.Location = new Point(30, 250);
            lblPrix.Size = new Size(120, 20);
            lblPrix.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.Controls.Add(lblPrix);

            nudPrix = new NumericUpDown();
            nudPrix.Location = new Point(160, 248);
            nudPrix.Size = new Size(130, 23);
            nudPrix.Maximum = 999999;
            nudPrix.DecimalPlaces = 2;
            nudPrix.Font = new Font("Segoe UI", 9F);
            this.Controls.Add(nudPrix);

            // Total Payé (calculé automatiquement)
            Label lblTotalPaye = new Label();
            lblTotalPaye.Text = "Total Payé: (Auto)";
            lblTotalPaye.Location = new Point(310, 250);
            lblTotalPaye.Size = new Size(100, 20);
            lblTotalPaye.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            this.Controls.Add(lblTotalPaye);

            nudTotalPaye = new NumericUpDown();
            nudTotalPaye.Location = new Point(415, 248);
            nudTotalPaye.Size = new Size(100, 23);
            nudTotalPaye.Maximum = 999999;
            nudTotalPaye.DecimalPlaces = 2;
            nudTotalPaye.Font = new Font("Segoe UI", 9F);
            nudTotalPaye.ReadOnly = true;
            nudTotalPaye.BackColor = Color.FromArgb(245, 245, 245);
            nudTotalPaye.Value = 0; // Sera calculé selon les statuts
            this.Controls.Add(nudTotalPaye);

            // Boutons
            btnSauvegarder = new Button();
            btnSauvegarder.Text = "Sauvegarder";
            btnSauvegarder.Location = new Point(250, 300);
            btnSauvegarder.Size = new Size(100, 35);
            btnSauvegarder.BackColor = Color.FromArgb(34, 139, 34);
            btnSauvegarder.ForeColor = Color.White;
            btnSauvegarder.FlatStyle = FlatStyle.Flat;
            btnSauvegarder.FlatAppearance.BorderSize = 0;
            btnSauvegarder.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnSauvegarder.Click += BtnSauvegarder_Click;
            this.Controls.Add(btnSauvegarder);

            btnAnnuler = new Button();
            btnAnnuler.Text = "Annuler";
            btnAnnuler.Location = new Point(360, 300);
            btnAnnuler.Size = new Size(100, 35);
            btnAnnuler.BackColor = Color.FromArgb(220, 20, 60);
            btnAnnuler.ForeColor = Color.White;
            btnAnnuler.FlatStyle = FlatStyle.Flat;
            btnAnnuler.FlatAppearance.BorderSize = 0;
            btnAnnuler.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnAnnuler.Click += BtnAnnuler_Click;
            this.Controls.Add(btnAnnuler);

            this.ResumeLayout(false);
        }

        private void BtnSauvegarder_Click(object sender, EventArgs e)
        {
            // Validation des champs
            if (string.IsNullOrWhiteSpace(txtRefClient.Text))
            {
                MessageBox.Show("Veuillez saisir la référence client.", "Validation", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtRefClient.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtNomPrenom.Text))
            {
                MessageBox.Show("Veuillez saisir le nom et prénom.", "Validation", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtNomPrenom.Focus();
                return;
            }

            if (string.IsNullOrWhiteSpace(txtArticles.Text))
            {
                MessageBox.Show("Veuillez saisir les articles.", "Validation", 
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
                txtArticles.Focus();
                return;
            }

            try
            {
                dbManager.AjouterCommande(
                    txtRefClient.Text.Trim(),
                    txtNomPrenom.Text.Trim(),
                    txtArticles.Text.Trim(),
                    dtpDateCommande.Value.ToString("dd/MM/yyyy"),
                    nudPrix.Value,
                    0 // Total Payé sera calculé automatiquement selon les statuts
                );

                MessageBox.Show("Commande ajoutée avec succès!\nLe Total Payé sera calculé automatiquement selon les statuts cochés (1-10).",
                              "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);

                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'ajout de la commande: {ex.Message}", 
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnAnnuler_Click(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
            this.Close();
        }
    }
}
