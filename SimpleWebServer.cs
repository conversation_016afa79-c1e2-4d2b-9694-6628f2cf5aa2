using System;
using System.Collections.Generic;
using System.IO;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace SimpleWebServer
{
    public class Program
    {
        private static HttpListener listener;
        private static bool isRunning = false;
        private static List<dynamic> commandes = new List<dynamic>();

        public static async Task Main(string[] args)
        {
            // إضافة بيانات تجريبية
            InitializeData();

            listener = new HttpListener();
            listener.Prefixes.Add("http://localhost:8080/");
            
            try
            {
                listener.Start();
                isRunning = true;
                Console.WriteLine("🌐 خادم الويب يعمل على: http://localhost:8080");
                Console.WriteLine("📊 واجهة الإدارة: http://localhost:8080/GestionnaireGROUPE02-Database.html");
                Console.WriteLine("اضغط Ctrl+C للإيقاف");
                
                Console.CancelKeyPress += (sender, e) => {
                    e.Cancel = true;
                    Stop();
                    Environment.Exit(0);
                };

                while (isRunning)
                {
                    try
                    {
                        var context = await listener.GetContextAsync();
                        _ = Task.Run(() => ProcessRequest(context));
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"خطأ في الخادم: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"فشل في بدء الخادم: {ex.Message}");
                Console.WriteLine("تأكد من تشغيل البرنامج كمدير");
            }
        }

        private static void Stop()
        {
            isRunning = false;
            listener?.Stop();
            listener?.Close();
        }

        private static void InitializeData()
        {
            commandes.Add(new {
                id = 1,
                refClient = "OS24S1793",
                nomPrenom = "BEN SEGHIR ABDERAHMANE",
                articles = "TELE REALME C65 8/256",
                dateCommande = "24/09/2024",
                prix = 48000.00,
                totalPaye = 28800.00,
                statuts = new int[] {1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0},
                nombreColonnes = 10
            });

            commandes.Add(new {
                id = 2,
                refClient = "OS24S1796",
                nomPrenom = "SIGA MOHAMMED",
                articles = "REF 580L+ TV 40\" VDDA+ TENDEUSE",
                dateCommande = "29/09/2024",
                prix = 142900.00,
                totalPaye = 85740.00,
                statuts = new int[] {1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0},
                nombreColonnes = 10
            });

            // إضافة المزيد من البيانات التجريبية...
            for (int i = 3; i <= 10; i++)
            {
                commandes.Add(new {
                    id = i,
                    refClient = $"OS24S{1790 + i}",
                    nomPrenom = $"CLIENT {i}",
                    articles = $"ARTICLE {i}",
                    dateCommande = DateTime.Now.AddDays(-i).ToString("dd/MM/yyyy"),
                    prix = 25000.00 + (i * 1000),
                    totalPaye = (25000.00 + (i * 1000)) * 0.6,
                    statuts = new int[] {1,1,1,1,1,1,0,0,0,0,0,0,0,0,0,0,0,0,0,0},
                    nombreColonnes = 10
                });
            }
        }

        private static async Task ProcessRequest(HttpListenerContext context)
        {
            var request = context.Request;
            var response = context.Response;

            // إضافة CORS headers
            response.Headers.Add("Access-Control-Allow-Origin", "*");
            response.Headers.Add("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS");
            response.Headers.Add("Access-Control-Allow-Headers", "Content-Type");

            try
            {
                if (request.HttpMethod == "OPTIONS")
                {
                    response.StatusCode = 200;
                    response.Close();
                    return;
                }

                string path = request.Url.AbsolutePath.ToLower();
                string method = request.HttpMethod;

                Console.WriteLine($"{method} {path}");

                string responseText = "";

                if (path.StartsWith("/api/commandes"))
                {
                    responseText = await HandleCommandesApi(method, request);
                }
                else if (path.StartsWith("/api/statuts"))
                {
                    responseText = await HandleStatutsApi(method, request);
                }
                else if (path == "/" || path.EndsWith(".html"))
                {
                    await ServeStaticFile(context, path);
                    return;
                }
                else
                {
                    response.StatusCode = 404;
                    responseText = JsonSerializer.Serialize(new { error = "المسار غير موجود" });
                }

                byte[] buffer = Encoding.UTF8.GetBytes(responseText);
                response.ContentType = "application/json; charset=utf-8";
                response.ContentLength64 = buffer.Length;
                await response.OutputStream.WriteAsync(buffer, 0, buffer.Length);
            }
            catch (Exception ex)
            {
                response.StatusCode = 500;
                string errorResponse = JsonSerializer.Serialize(new { error = ex.Message });
                byte[] errorBuffer = Encoding.UTF8.GetBytes(errorResponse);
                await response.OutputStream.WriteAsync(errorBuffer, 0, errorBuffer.Length);
            }
            finally
            {
                response.Close();
            }
        }

        private static async Task<string> HandleCommandesApi(string method, HttpListenerRequest request)
        {
            switch (method)
            {
                case "GET":
                    return JsonSerializer.Serialize(new { success = true, data = commandes });
                
                case "POST":
                    using (var reader = new StreamReader(request.InputStream))
                    {
                        string body = await reader.ReadToEndAsync();
                        var commandeData = JsonSerializer.Deserialize<JsonElement>(body);
                        
                        var newId = commandes.Count + 1;
                        var newCommande = new {
                            id = newId,
                            refClient = commandeData.GetProperty("refClient").GetString(),
                            nomPrenom = commandeData.GetProperty("nomPrenom").GetString(),
                            articles = commandeData.GetProperty("articles").GetString(),
                            dateCommande = commandeData.GetProperty("dateCommande").GetString(),
                            prix = commandeData.GetProperty("prix").GetDouble(),
                            totalPaye = 0.0,
                            statuts = new int[20],
                            nombreColonnes = 10
                        };
                        
                        commandes.Add(newCommande);
                        return JsonSerializer.Serialize(new { success = true, message = "تم إضافة الطلب بنجاح" });
                    }
                
                case "DELETE":
                    string query = request.Url.Query;
                    if (query.StartsWith("?id="))
                    {
                        int id = int.Parse(query.Substring(4));
                        commandes.RemoveAll(c => ((dynamic)c).id == id);
                        return JsonSerializer.Serialize(new { success = true, message = "تم حذف الطلب بنجاح" });
                    }
                    break;
            }
            
            return JsonSerializer.Serialize(new { error = "عملية غير مدعومة" });
        }

        private static async Task<string> HandleStatutsApi(string method, HttpListenerRequest request)
        {
            if (method == "PUT")
            {
                using (var reader = new StreamReader(request.InputStream))
                {
                    string body = await reader.ReadToEndAsync();
                    var statutData = JsonSerializer.Deserialize<JsonElement>(body);
                    
                    int id = statutData.GetProperty("id").GetInt32();
                    int index = statutData.GetProperty("index").GetInt32();
                    bool value = statutData.GetProperty("value").GetBoolean();
                    
                    // العثور على الطلب وتحديث الحالة
                    for (int i = 0; i < commandes.Count; i++)
                    {
                        dynamic commande = commandes[i];
                        if (commande.id == id)
                        {
                            var statuts = (int[])commande.statuts;
                            statuts[index] = value ? 1 : 0;
                            
                            // إعادة حساب المبلغ المدفوع
                            int totalStatuts = 0;
                            for (int j = 0; j < commande.nombreColonnes; j++)
                            {
                                totalStatuts += statuts[j];
                            }
                            
                            var newCommande = new {
                                id = commande.id,
                                refClient = commande.refClient,
                                nomPrenom = commande.nomPrenom,
                                articles = commande.articles,
                                dateCommande = commande.dateCommande,
                                prix = commande.prix,
                                totalPaye = (totalStatuts * commande.prix) / commande.nombreColonnes,
                                statuts = statuts,
                                nombreColonnes = commande.nombreColonnes
                            };
                            
                            commandes[i] = newCommande;
                            break;
                        }
                    }
                    
                    return JsonSerializer.Serialize(new { success = true, message = "تم تحديث الحالة بنجاح" });
                }
            }
            
            return JsonSerializer.Serialize(new { error = "عملية غير مدعومة" });
        }

        private static async Task ServeStaticFile(HttpListenerContext context, string path)
        {
            if (path == "/")
                path = "/GestionnaireGroupes.html";
            
            string filePath = "." + path;
            
            if (File.Exists(filePath))
            {
                string content = await File.ReadAllTextAsync(filePath);
                byte[] buffer = Encoding.UTF8.GetBytes(content);
                
                context.Response.ContentType = GetContentType(path);
                context.Response.ContentLength64 = buffer.Length;
                await context.Response.OutputStream.WriteAsync(buffer, 0, buffer.Length);
            }
            else
            {
                context.Response.StatusCode = 404;
                string notFound = "الملف غير موجود";
                byte[] buffer = Encoding.UTF8.GetBytes(notFound);
                await context.Response.OutputStream.WriteAsync(buffer, 0, buffer.Length);
            }
        }

        private static string GetContentType(string path)
        {
            string extension = Path.GetExtension(path).ToLower();
            return extension switch
            {
                ".html" => "text/html; charset=utf-8",
                ".css" => "text/css",
                ".js" => "application/javascript",
                ".json" => "application/json",
                _ => "text/plain"
            };
        }
    }
}
