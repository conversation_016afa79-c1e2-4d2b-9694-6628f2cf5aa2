using System;
using System.Data;
using System.Drawing;
using System.Windows.Forms;
using System.IO;
using OfficeOpenXml;
using OfficeOpenXml.Style;

namespace CustomerOrderManager
{
    public partial class MainForm : Form
    {
        private DatabaseManager dbManager;
        private DataGridView dataGridView;
        private Button btnAjouter;
        private Button btnSupprimer;
        private Button btnActualiser;
        private Label lblTitre;
        private Panel panelBoutons;
        private Label lblStatistiques;
        private TextBox txtNombreColonnes;
        private Label lblNombreColonnes;
        private Button btnAppliquerColonnes;
        private Button btnExporterExcel;
        private Button btnGenererFacture;

        public MainForm()
        {
            InitializeComponent();
            dbManager = new DatabaseManager();

            // Recalculer tous les totaux au démarrage pour s'assurer de la cohérence
            dbManager.RecalculerTousLesTotaux();

            ChargerDonnees();
        }

        private void InitializeComponent()
        {
            this.SuspendLayout();

            // Configuration de la fenêtre principale
            this.Text = "Gestionnaire de Commandes Clients - GROUPE 02";
            this.Size = new Size(1200, 700);
            this.StartPosition = FormStartPosition.CenterScreen;
            this.BackColor = Color.FromArgb(240, 240, 240);
            this.Font = new Font("Segoe UI", 9F, FontStyle.Regular);

            // Titre
            lblTitre = new Label();
            lblTitre.Text = "GESTIONNAIRE DE COMMANDES CLIENTS - GROUPE 02";
            lblTitre.Font = new Font("Arial", 14F, FontStyle.Bold);
            lblTitre.ForeColor = Color.FromArgb(0, 51, 102);
            lblTitre.TextAlign = ContentAlignment.MiddleCenter;
            lblTitre.Dock = DockStyle.Top;
            lblTitre.Height = 50;
            lblTitre.BackColor = Color.FromArgb(220, 230, 240);
            this.Controls.Add(lblTitre);

            // Panel pour les boutons
            panelBoutons = new Panel();
            panelBoutons.Height = 60;
            panelBoutons.Dock = DockStyle.Top;
            panelBoutons.BackColor = Color.FromArgb(230, 230, 230);
            panelBoutons.Padding = new Padding(10);

            // Bouton Ajouter
            btnAjouter = new Button();
            btnAjouter.Text = "Ajouter Commande";
            btnAjouter.Size = new Size(150, 35);
            btnAjouter.Location = new Point(10, 12);
            btnAjouter.BackColor = Color.FromArgb(70, 130, 180);
            btnAjouter.ForeColor = Color.White;
            btnAjouter.FlatStyle = FlatStyle.Flat;
            btnAjouter.FlatAppearance.BorderSize = 0;
            btnAjouter.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnAjouter.Click += BtnAjouter_Click;
            panelBoutons.Controls.Add(btnAjouter);

            // Bouton Supprimer
            btnSupprimer = new Button();
            btnSupprimer.Text = "Supprimer";
            btnSupprimer.Size = new Size(120, 35);
            btnSupprimer.Location = new Point(170, 12);
            btnSupprimer.BackColor = Color.FromArgb(220, 20, 60);
            btnSupprimer.ForeColor = Color.White;
            btnSupprimer.FlatStyle = FlatStyle.Flat;
            btnSupprimer.FlatAppearance.BorderSize = 0;
            btnSupprimer.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnSupprimer.Click += BtnSupprimer_Click;
            panelBoutons.Controls.Add(btnSupprimer);

            // Bouton Actualiser
            btnActualiser = new Button();
            btnActualiser.Text = "Actualiser";
            btnActualiser.Size = new Size(120, 35);
            btnActualiser.Location = new Point(300, 12);
            btnActualiser.BackColor = Color.FromArgb(34, 139, 34);
            btnActualiser.ForeColor = Color.White;
            btnActualiser.FlatStyle = FlatStyle.Flat;
            btnActualiser.FlatAppearance.BorderSize = 0;
            btnActualiser.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnActualiser.Click += BtnActualiser_Click;
            panelBoutons.Controls.Add(btnActualiser);

            // Label pour nombre de colonnes
            lblNombreColonnes = new Label();
            lblNombreColonnes.Text = "Nombre de colonnes:";
            lblNombreColonnes.Location = new Point(450, 15);
            lblNombreColonnes.Size = new Size(120, 20);
            lblNombreColonnes.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            panelBoutons.Controls.Add(lblNombreColonnes);

            // TextBox pour nombre de colonnes
            txtNombreColonnes = new TextBox();
            txtNombreColonnes.Location = new Point(575, 12);
            txtNombreColonnes.Size = new Size(50, 23);
            txtNombreColonnes.Text = "10";
            txtNombreColonnes.Font = new Font("Segoe UI", 9F);
            txtNombreColonnes.TextAlign = HorizontalAlignment.Center;
            panelBoutons.Controls.Add(txtNombreColonnes);

            // Bouton Appliquer
            btnAppliquerColonnes = new Button();
            btnAppliquerColonnes.Text = "Appliquer";
            btnAppliquerColonnes.Size = new Size(80, 35);
            btnAppliquerColonnes.Location = new Point(635, 12);
            btnAppliquerColonnes.BackColor = Color.FromArgb(70, 130, 180);
            btnAppliquerColonnes.ForeColor = Color.White;
            btnAppliquerColonnes.FlatStyle = FlatStyle.Flat;
            btnAppliquerColonnes.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnAppliquerColonnes.Click += BtnAppliquerColonnes_Click;
            panelBoutons.Controls.Add(btnAppliquerColonnes);

            // Bouton Exporter Excel
            btnExporterExcel = new Button();
            btnExporterExcel.Text = "📊 Exporter Excel";
            btnExporterExcel.Size = new Size(130, 35);
            btnExporterExcel.Location = new Point(725, 12);
            btnExporterExcel.BackColor = Color.FromArgb(34, 139, 34);
            btnExporterExcel.ForeColor = Color.White;
            btnExporterExcel.FlatStyle = FlatStyle.Flat;
            btnExporterExcel.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnExporterExcel.Click += BtnExporterExcel_Click;
            panelBoutons.Controls.Add(btnExporterExcel);

            // Bouton Générer Facture
            btnGenererFacture = new Button();
            btnGenererFacture.Text = "🧾 Générer Facture";
            btnGenererFacture.Size = new Size(140, 35);
            btnGenererFacture.Location = new Point(865, 12);
            btnGenererFacture.BackColor = Color.FromArgb(68, 114, 196);
            btnGenererFacture.ForeColor = Color.White;
            btnGenererFacture.FlatStyle = FlatStyle.Flat;
            btnGenererFacture.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            btnGenererFacture.Click += BtnGenererFacture_Click;
            panelBoutons.Controls.Add(btnGenererFacture);

            this.Controls.Add(panelBoutons);

            // Panel pour les statistiques
            lblStatistiques = new Label();
            lblStatistiques.Height = 30;
            lblStatistiques.Dock = DockStyle.Top;
            lblStatistiques.BackColor = Color.FromArgb(240, 248, 255);
            lblStatistiques.ForeColor = Color.FromArgb(0, 51, 102);
            lblStatistiques.Font = new Font("Segoe UI", 10F, FontStyle.Bold);
            lblStatistiques.TextAlign = ContentAlignment.MiddleCenter;
            lblStatistiques.Text = "TOTAL GÉNÉRAL: 0.00 DA | TOTAL PAYÉ: 0.00 DA | RESTANT: 0.00 DA";
            this.Controls.Add(lblStatistiques);

            // DataGridView
            dataGridView = new DataGridView();
            dataGridView.Dock = DockStyle.Fill;
            dataGridView.BackgroundColor = Color.White;
            dataGridView.BorderStyle = BorderStyle.Fixed3D;
            dataGridView.CellBorderStyle = DataGridViewCellBorderStyle.Single;
            dataGridView.GridColor = Color.FromArgb(200, 200, 200);
            dataGridView.AllowUserToAddRows = false;
            dataGridView.AllowUserToDeleteRows = false;
            dataGridView.SelectionMode = DataGridViewSelectionMode.FullRowSelect;
            dataGridView.MultiSelect = false;
            dataGridView.ReadOnly = false;
            dataGridView.AutoSizeColumnsMode = DataGridViewAutoSizeColumnsMode.Fill;
            
            // Style des en-têtes
            dataGridView.ColumnHeadersDefaultCellStyle.BackColor = Color.FromArgb(70, 130, 180);
            dataGridView.ColumnHeadersDefaultCellStyle.ForeColor = Color.White;
            dataGridView.ColumnHeadersDefaultCellStyle.Font = new Font("Segoe UI", 9F, FontStyle.Bold);
            dataGridView.ColumnHeadersHeight = 35;
            
            // Style des lignes
            dataGridView.DefaultCellStyle.BackColor = Color.White;
            dataGridView.DefaultCellStyle.ForeColor = Color.Black;
            dataGridView.DefaultCellStyle.SelectionBackColor = Color.FromArgb(173, 216, 230);
            dataGridView.DefaultCellStyle.SelectionForeColor = Color.Black;
            dataGridView.AlternatingRowsDefaultCellStyle.BackColor = Color.FromArgb(248, 248, 248);
            dataGridView.CellValueChanged += DataGridView_CellValueChanged;

            this.Controls.Add(dataGridView);

            this.ResumeLayout(false);
        }

        private void ChargerDonnees()
        {
            try
            {
                DataTable dt = dbManager.GetToutesCommandes();
                dataGridView.DataSource = dt;
                
                if (dataGridView.Columns.Count > 0)
                {
                    ConfigurerColonnes();
                    MettreAJourStatistiques();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors du chargement des données: {ex.Message}", 
                              "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ConfigurerColonnes()
        {
            // Configuration des en-têtes de colonnes en français
            if (dataGridView.Columns["Id"] != null)
                dataGridView.Columns["Id"].HeaderText = "N°";
            if (dataGridView.Columns["RefClient"] != null)
                dataGridView.Columns["RefClient"].HeaderText = "REF CLI";
            if (dataGridView.Columns["NomPrenom"] != null)
                dataGridView.Columns["NomPrenom"].HeaderText = "NOM ET PRÉNOM";
            if (dataGridView.Columns["Articles"] != null)
                dataGridView.Columns["Articles"].HeaderText = "ARTICLES COM";
            if (dataGridView.Columns["DateCommande"] != null)
                dataGridView.Columns["DateCommande"].HeaderText = "DATE DE COM";
            if (dataGridView.Columns["Prix"] != null)
                dataGridView.Columns["Prix"].HeaderText = "PRIX";
            if (dataGridView.Columns["TotalPaye"] != null)
                dataGridView.Columns["TotalPaye"].HeaderText = "TOTAL PAYÉ";
            if (dataGridView.Columns["Restant"] != null)
                dataGridView.Columns["Restant"].HeaderText = "RESTANT";

            // Configuration des colonnes de statut selon le nombre choisi
            int nombreColonnes = int.Parse(txtNombreColonnes.Text);
            for (int i = 1; i <= 10; i++)
            {
                string colName = $"Statut{i}";
                if (dataGridView.Columns[colName] != null)
                {
                    if (i <= nombreColonnes)
                    {
                        dataGridView.Columns[colName].Visible = true;
                        dataGridView.Columns[colName].HeaderText = i.ToString();
                        dataGridView.Columns[colName].Width = 40;
                    }
                    else
                    {
                        dataGridView.Columns[colName].Visible = false;
                    }
                }
            }

            // Masquer la colonne DateCreation si elle existe
            if (dataGridView.Columns["DateCreation"] != null)
                dataGridView.Columns["DateCreation"].Visible = false;
        }

        private void BtnAjouter_Click(object sender, EventArgs e)
        {
            AjouterCommandeForm form = new AjouterCommandeForm();
            if (form.ShowDialog() == DialogResult.OK)
            {
                ChargerDonnees();
            }
        }

        private void BtnSupprimer_Click(object sender, EventArgs e)
        {
            if (dataGridView.SelectedRows.Count > 0)
            {
                var result = MessageBox.Show("Êtes-vous sûr de vouloir supprimer cette commande?", 
                                           "Confirmation", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                if (result == DialogResult.Yes)
                {
                    int id = Convert.ToInt32(dataGridView.SelectedRows[0].Cells["Id"].Value);
                    dbManager.SupprimerCommande(id);
                    ChargerDonnees();
                }
            }
            else
            {
                MessageBox.Show("Veuillez sélectionner une commande à supprimer.", 
                              "Information", MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
        }

        private void BtnActualiser_Click(object sender, EventArgs e)
        {
            ChargerDonnees();
        }

        private void DataGridView_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            // Vérifier si c'est une colonne de statut (Statut1 à Statut10)
            if (e.RowIndex >= 0 && e.ColumnIndex >= 0)
            {
                string columnName = dataGridView.Columns[e.ColumnIndex].Name;
                if (columnName.StartsWith("Statut") && columnName.Length <= 8)
                {
                    try
                    {
                        int commandeId = Convert.ToInt32(dataGridView.Rows[e.RowIndex].Cells["Id"].Value);
                        int numeroStatut = int.Parse(columnName.Substring(6)); // Extraire le numéro après "Statut"
                        bool estCoche = Convert.ToBoolean(dataGridView.Rows[e.RowIndex].Cells[columnName].Value);
                        int nombreColonnes = int.Parse(txtNombreColonnes.Text);

                        dbManager.MettreAJourStatut(commandeId, numeroStatut, estCoche, nombreColonnes);

                        // Recharger les données pour afficher le nouveau Total Payé
                        ChargerDonnees();
                        MettreAJourStatistiques();
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show($"Erreur lors de la mise à jour du statut: {ex.Message}",
                                      "Erreur", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
            }
        }

        private void MettreAJourStatistiques()
        {
            try
            {
                DataTable dt = dbManager.GetToutesCommandes();
                decimal totalGeneral = 0;
                decimal totalPaye = 0;

                foreach (DataRow row in dt.Rows)
                {
                    totalGeneral += Convert.ToDecimal(row["Prix"]);
                    totalPaye += Convert.ToDecimal(row["TotalPaye"]);
                }

                decimal totalRestant = totalGeneral - totalPaye;

                lblStatistiques.Text = $"TOTAL GÉNÉRAL: {totalGeneral:F2} DA | TOTAL PAYÉ: {totalPaye:F2} DA | RESTANT: {totalRestant:F2} DA";

                // Changer la couleur selon le statut
                if (totalRestant <= 0)
                {
                    lblStatistiques.ForeColor = Color.FromArgb(34, 139, 34); // Vert si tout est payé
                }
                else
                {
                    lblStatistiques.ForeColor = Color.FromArgb(220, 20, 60); // Rouge s'il reste à payer
                }
            }
            catch (Exception ex)
            {
                lblStatistiques.Text = "Erreur lors du calcul des statistiques";
                lblStatistiques.ForeColor = Color.Red;
            }
        }

        private void BtnAppliquerColonnes_Click(object sender, EventArgs e)
        {
            try
            {
                int nombreColonnes = int.Parse(txtNombreColonnes.Text);

                if (nombreColonnes < 1 || nombreColonnes > 20)
                {
                    MessageBox.Show("Le nombre de colonnes doit être entre 1 et 20.", "Erreur",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Mettre à jour la structure de la base de données
                dbManager.MettreAJourNombreColonnes(nombreColonnes);

                // Recharger les données
                ChargerDonnees();
                MettreAJourStatistiques();

                MessageBox.Show($"Nombre de colonnes mis à jour: {nombreColonnes}", "Succès",
                              MessageBoxButtons.OK, MessageBoxIcon.Information);
            }
            catch (FormatException)
            {
                MessageBox.Show("Veuillez saisir un nombre valide.", "Erreur",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur: {ex.Message}", "Erreur",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void BtnExporterExcel_Click(object sender, EventArgs e)
        {
            try
            {
                // Configuration EPPlus pour usage non commercial
                ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

                SaveFileDialog saveDialog = new SaveFileDialog();
                saveDialog.Filter = "Fichiers Excel (*.xlsx)|*.xlsx";
                saveDialog.Title = "Enregistrer la fature Excel";
                saveDialog.FileName = $"Facture_Commandes_{DateTime.Now:yyyy-MM-dd_HH-mm}.xlsx";

                if (saveDialog.ShowDialog() == DialogResult.OK)
                {
                    GenererFactureExcel(saveDialog.FileName);
                    MessageBox.Show($"Facture Excel générée avec succès!\n\nFichier: {saveDialog.FileName}",
                                  "Succès", MessageBoxButtons.OK, MessageBoxIcon.Information);

                    // Demander si l'utilisateur veut ouvrir le fichier
                    var result = MessageBox.Show("Voulez-vous ouvrir le fichier Excel maintenant?",
                                               "Ouvrir le fichier", MessageBoxButtons.YesNo, MessageBoxIcon.Question);
                    if (result == DialogResult.Yes)
                    {
                        System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo()
                        {
                            FileName = saveDialog.FileName,
                            UseShellExecute = true
                        });
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'exportation: {ex.Message}", "Erreur",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void GenererFactureExcel(string cheminFichier)
        {
            using (var package = new ExcelPackage())
            {
                var worksheet = package.Workbook.Worksheets.Add("Facture Commandes");

                // En-tête de la facture
                worksheet.Cells["A1:P1"].Merge = true;
                worksheet.Cells["A1"].Value = "GESTIONNAIRE DE COMMANDES CLIENTS - GROUPE 02";
                worksheet.Cells["A1"].Style.Font.Size = 16;
                worksheet.Cells["A1"].Style.Font.Bold = true;
                worksheet.Cells["A1"].Style.HorizontalAlignment = ExcelHorizontalAlignment.Center;
                worksheet.Cells["A1"].Style.Fill.PatternType = ExcelFillPatternType.Solid;
                worksheet.Cells["A1"].Style.Fill.BackgroundColor.SetColor(Color.FromArgb(70, 130, 180));
                worksheet.Cells["A1"].Style.Font.Color.SetColor(Color.White);

                // Informations de la facture
                worksheet.Cells["A3"].Value = "Date de génération:";
                worksheet.Cells["B3"].Value = DateTime.Now.ToString("dd/MM/yyyy HH:mm");
                worksheet.Cells["A4"].Value = "Nombre total de commandes:";
                worksheet.Cells["B4"].Value = dataGridView.Rows.Count;

                // En-têtes des colonnes
                int row = 6;
                worksheet.Cells[row, 1].Value = "N°";
                worksheet.Cells[row, 2].Value = "REF CLI";
                worksheet.Cells[row, 3].Value = "NOM ET PRÉNOM";
                worksheet.Cells[row, 4].Value = "ARTICLES COM";
                worksheet.Cells[row, 5].Value = "DATE DE COM";
                worksheet.Cells[row, 6].Value = "PRIX";
                worksheet.Cells[row, 7].Value = "TOTAL PAYÉ";
                worksheet.Cells[row, 8].Value = "RESTANT";

                // Ajouter les colonnes de statut selon le nombre choisi
                int nombreColonnes = int.Parse(txtNombreColonnes.Text);
                for (int i = 1; i <= nombreColonnes; i++)
                {
                    worksheet.Cells[row, 8 + i].Value = i.ToString();
                }

                // Style des en-têtes
                var headerRange = worksheet.Cells[row, 1, row, 8 + nombreColonnes];
                headerRange.Style.Font.Bold = true;
                headerRange.Style.Fill.PatternType = ExcelFillPatternType.Solid;
                headerRange.Style.Fill.BackgroundColor.SetColor(Color.FromArgb(220, 230, 240));
                headerRange.Style.Border.BorderAround(ExcelBorderStyle.Thick);

                // Données des commandes
                row++;
                DataTable dataTable = dbManager.GetToutesCommandes();
                foreach (DataRow dataRow in dataTable.Rows)
                {
                    worksheet.Cells[row, 1].Value = dataRow["Id"];
                    worksheet.Cells[row, 2].Value = dataRow["RefClient"];
                    worksheet.Cells[row, 3].Value = dataRow["NomPrenom"];
                    worksheet.Cells[row, 4].Value = dataRow["Articles"];
                    worksheet.Cells[row, 5].Value = dataRow["DateCommande"];
                    worksheet.Cells[row, 6].Value = Convert.ToDecimal(dataRow["Prix"]);
                    worksheet.Cells[row, 7].Value = Convert.ToDecimal(dataRow["TotalPaye"]);
                    worksheet.Cells[row, 8].Value = Convert.ToDecimal(dataRow["Restant"]);

                    // Ajouter les statuts
                    for (int i = 1; i <= nombreColonnes; i++)
                    {
                        string statutColumn = $"Statut{i}";
                        if (dataTable.Columns.Contains(statutColumn))
                        {
                            bool statut = Convert.ToBoolean(dataRow[statutColumn]);
                            worksheet.Cells[row, 8 + i].Value = statut ? "✓" : "";
                        }
                    }

                    row++;
                }

                // Statistiques en bas
                row += 2;
                decimal totalGeneral = 0, totalPaye = 0, totalRestant = 0;
                foreach (DataRow dataRow in dataTable.Rows)
                {
                    totalGeneral += Convert.ToDecimal(dataRow["Prix"]);
                    totalPaye += Convert.ToDecimal(dataRow["TotalPaye"]);
                    totalRestant += Convert.ToDecimal(dataRow["Restant"]);
                }

                worksheet.Cells[row, 1].Value = "TOTAUX:";
                worksheet.Cells[row, 6].Value = totalGeneral;
                worksheet.Cells[row, 7].Value = totalPaye;
                worksheet.Cells[row, 8].Value = totalRestant;

                // Style des totaux
                var totalRange = worksheet.Cells[row, 1, row, 8 + nombreColonnes];
                totalRange.Style.Font.Bold = true;
                totalRange.Style.Fill.PatternType = ExcelFillPatternType.Solid;
                totalRange.Style.Fill.BackgroundColor.SetColor(Color.FromArgb(255, 255, 200));

                // Ajustement automatique des colonnes
                worksheet.Cells.AutoFitColumns();

                // Sauvegarde
                FileInfo file = new FileInfo(cheminFichier);
                package.SaveAs(file);
            }
        }

        private void BtnGenererFacture_Click(object sender, EventArgs e)
        {
            try
            {
                string cheminFacture = Path.Combine(Application.StartupPath, "FactureTemplate.html");

                if (File.Exists(cheminFacture))
                {
                    // Ouvrir la facture dans le navigateur par défaut
                    System.Diagnostics.Process.Start(new System.Diagnostics.ProcessStartInfo()
                    {
                        FileName = cheminFacture,
                        UseShellExecute = true
                    });
                }
                else
                {
                    MessageBox.Show("Le fichier de modèle de facture n'a pas été trouvé.\n\nAssurez-vous que FactureTemplate.html est dans le même dossier que l'application.",
                                  "Fichier manquant", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Erreur lors de l'ouverture de la facture: {ex.Message}", "Erreur",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
