using System;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace CustomerOrderManager
{
    internal static class Program
    {
        /// <summary>
        /// Point d'entrée principal de l'application.
        /// </summary>
        [STAThread]
        static async Task Main(string[] args)
        {
            // التحقق من وسائط سطر الأوامر
            if (args.Length > 0 && args[0] == "--web")
            {
                // تشغيل خادم الويب
                Console.WriteLine("🚀 بدء تشغيل خادم الويب...");
                var webApi = new WebApiController();

                Console.WriteLine("اضغط Ctrl+C للإيقاف");
                Console.CancelKeyPress += (sender, e) => {
                    e.Cancel = true;
                    webApi.Stop();
                    Environment.Exit(0);
                };

                await webApi.StartAsync();
            }
            else
            {
                // Configuration de l'application pour les styles visuels modernes
                Application.EnableVisualStyles();
                Application.SetCompatibleTextRenderingDefault(false);

                // Démarrage de l'application avec le formulaire principal
                Application.Run(new MainForm());
            }
        }
    }
}
