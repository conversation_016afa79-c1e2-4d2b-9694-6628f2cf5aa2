<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Facture - DJEMIAL MOUSTAPHA</title>
    <style>
        @page {
            size: A4 portrait;
            margin: 10mm 8mm 10mm 8mm;
            -webkit-print-color-adjust: exact;
            print-color-adjust: exact;
        }
        
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: Arial, sans-serif;
            font-size: 11px;
            line-height: 1.3;
            background: white;
            margin: 0;
            padding: 0;
        }
        
        .facture-container {
            width: 100%;
            max-width: 210mm;
            min-height: 297mm;
            margin: 0 auto;
            background: white;
            border: 2px solid #000;
            box-sizing: border-box;
            padding: 3mm;
        }
        
        /* En-tête avec informations de l'entreprise */
        .header-section {
            display: flex;
            border-bottom: 2px solid #000;
        }
        
        .company-info {
            flex: 1;
            padding: 8px;
            border-right: 2px solid #000;
            font-size: 10px;
        }

        .company-info h3 {
            font-weight: bold;
            margin-bottom: 4px;
            font-size: 12px;
        }

        .company-info div {
            margin-bottom: 2px;
            line-height: 1.2;
        }
        
        .logo-section {
            width: 100px;
            padding: 12px;
            text-align: center;
            border-right: 2px solid #000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .logo {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: linear-gradient(45deg, #ff6b35, #f7931e, #ffd700, #ff6b35);
            background-size: 200% 200%;
            animation: gradientShift 3s ease infinite;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
            color: white;
            font-weight: bold;
            font-size: 11px;
            text-align: center;
            border: 3px solid #333;
            overflow: hidden;
            position: relative;
            cursor: pointer;
        }

        .logo img {
            width: 100%;
            height: 100%;
            object-fit: cover;
            border-radius: 50%;
        }

        .logo-text {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            z-index: 1;
        }

        .logo-upload {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
            z-index: 2;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .facture-title {
            width: 120px;
            padding: 10px;
            text-align: center;
            border-right: 2px solid #000;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .facture-title h2 {
            background: #ffd700;
            padding: 12px 18px;
            border: 3px solid #000;
            font-size: 16px;
            font-weight: bold;
            letter-spacing: 1px;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
        }
        
        .registration-info {
            flex: 1;
            padding: 8px;
            font-size: 9px;
        }

        .registration-info div {
            margin-bottom: 2px;
            line-height: 1.2;
        }
        
        /* Section date et numéro de facture */
        .date-section {
            display: flex;
            background: #4472C4;
            color: white;
            font-weight: bold;
        }
        
        .date-left {
            flex: 1;
            padding: 6px;
            text-align: center;
            font-size: 11px;
        }

        .date-right {
            flex: 1;
            padding: 6px;
            text-align: center;
            border-left: 1px solid white;
            font-size: 11px;
        }
        
        /* Tableau des articles */
        .table-container {
            width: 100%;
            border: 3px solid #000;
            margin: 2px 0;
        }
        
        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
            border: 2px solid #000;
            margin: 2mm 0;
        }

        .table-header {
            background: #4472C4;
            color: white;
            font-weight: bold;
        }

        .table-header th {
            padding: 12px 7px;
            text-align: center;
            border: 2px solid #000;
            font-size: 12px;
            font-weight: bold;
            border-right: 2px solid #000;
            border-bottom: 2px solid #000;
            vertical-align: middle;
            line-height: 1.3;
        }

        .table-header th:last-child {
            border-right: 2px solid #000;
        }
        
        tbody tr:nth-child(odd) {
            background: #f8f9fa;
        }
        
        tbody tr:nth-child(even) {
            background: white;
        }
        
        td {
            padding: 8px 6px;
            text-align: center;
            border: 1px solid #000;
            font-size: 11px;
            border-right: 1px solid #000;
            border-bottom: 1px solid #000;
            vertical-align: middle;
            line-height: 1.4;
        }

        tbody tr td:first-child {
            border-left: 2px solid #000;
        }

        tbody tr td:last-child {
            border-right: 2px solid #000;
        }
        
        .text-left {
            text-align: left !important;
        }
        
        .text-right {
            text-align: right !important;
        }
        
        /* Ligne total */
        .total-row {
            background: #e6f2ff !important;
            font-weight: bold;
            border-top: 3px solid #000 !important;
        }

        .total-row td {
            border: 2px solid #000 !important;
            padding: 12px 7px !important;
            font-size: 12px !important;
            font-weight: bold !important;
        }
        
        /* Section signature */
        .signature-section {
            margin-top: 20px;
            padding: 15px;
            border: 1px solid #000;
            text-align: center;
            font-size: 10px;
            background: #f8f9fa;
        }
        
        /* Grille pour signature */
        .grid-section {
            margin-top: 5px;
            height: 80px;
            background-image:
                linear-gradient(to right, #ddd 1px, transparent 1px),
                linear-gradient(to bottom, #ddd 1px, transparent 1px);
            background-size: 15px 15px;
            border: 1px solid #ccc;
        }
        
        @media print {
            @page {
                size: A4 portrait;
                margin: 8mm;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            body {
                margin: 0;
                padding: 0;
                font-size: 11px;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .facture-container {
                border: 2px solid #000 !important;
                box-shadow: none;
                max-width: none;
                width: 100%;
                min-height: auto;
                padding: 2mm;
                page-break-inside: avoid;
            }

            .no-print {
                display: none !important;
            }

            table {
                page-break-inside: auto;
                font-size: 10px !important;
            }

            .table-header th {
                padding: 6px 4px !important;
                font-size: 10px !important;
            }

            td {
                padding: 5px 3px !important;
                font-size: 9px !important;
                line-height: 1.3 !important;
            }

            .total-row td {
                padding: 7px 4px !important;
                font-size: 10px !important;
            }

            #montantEnLettres {
                font-size: 10px !important;
                padding: 5px !important;
                margin: 5px 0 !important;
            }

            .table-header th {
                background: #4472C4 !important;
                color: white !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .total-row {
                background: #e6f2ff !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .logo {
                background: linear-gradient(45deg, #ff6b35, #f7931e, #ffd700) !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                animation: none !important;
                box-shadow: none !important;
            }

            .facture-title h2 {
                background: #ffd700 !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
                text-shadow: none !important;
                font-size: 14px !important;
            }
        }
    </style>
</head>
<body>
    <div class="facture-container">
        <!-- En-tête -->
        <div class="header-section">
            <div class="company-info">
                <h3>DJEMIAL MOUSTAPHA</h3>
                <div>CITE 500 LOGEMENT - LAGHOUAT</div>
                <div>TEL : 07-77-98-38</div>
                <div>E-mail : <EMAIL></div>
            </div>
            
            <div class="logo-section">
                <div class="logo" onclick="document.getElementById('logoUpload').click()" title="Cliquer pour changer le logo">
                    <input type="file" id="logoUpload" class="logo-upload" accept="image/*" onchange="changerLogo(this)">
                    <img id="logoImage" style="display: none;">
                    <div class="logo-text" id="logoText">
                        Facilite<br>Chaef
                    </div>
                </div>
            </div>
            
            <div class="facture-title">
                <h2>FACTURE</h2>
            </div>
            
            <div class="registration-info">
                <div><strong>N° REGISTRE DE COMMERCE :</strong> 03/00-4639233A21</div>
                <div><strong>N° COMPTE COMMERCIAL :</strong> BANQUE AGB 288614/208/09</div>
                <div><strong>N° NIS :</strong> 199030010184437</div>
                <div><strong>N° NIF :</strong> 230103222210</div>
            </div>
        </div>
        
        <!-- Section date -->
        <div class="date-section">
            <div class="date-left">DATE: <span id="currentDate">21/10/2024</span></div>
            <div class="date-right">FACTURE GROUPE N° 02 | Versement Echelonné N° <span id="versementNum">1</span> /10</div>
        </div>
        
        <!-- Tableau -->
        <div class="table-container">
            <table>
                <thead class="table-header">
                    <tr>
                        <th style="width: 8%;">N°</th>
                        <th style="width: 12%;">REF CLIENT</th>
                        <th style="width: 18%;">NOM ET PRENOM</th>
                        <th style="width: 25%;">ARTICLES</th>
                        <th style="width: 12%;">DATE D'ACHAT</th>
                        <th style="width: 10%;">PRIX</th>
                        <th style="width: 10%;">TOTAL PAYE</th>
                        <th style="width: 5%;">ECHEANCE N°</th>
                    </tr>
                </thead>
                <tbody id="factureTableBody">
                    <!-- Les données seront ajoutées ici par JavaScript -->
                </tbody>
            </table>
        </div>
        
        <!-- Section signature avec macro -->
        <div class="signature-section">
            <div id="montantEnLettres" style="text-align: center; font-weight: bold; font-size: 12px; margin: 10px 0; padding: 10px; border: 1px solid #000; background-color: #f8f9fa;">
                Arrêtée la présente facture à la somme de : <span id="montantTexte">zéro</span> dinars algériens
            </div>
        </div>
        
        <!-- Grille pour signatures -->
        <div class="grid-section"></div>
    </div>

    <!-- Boutons de contrôle (cachés lors de l'impression) -->
    <div class="no-print" style="text-align: center; margin: 20px;">
        <button onclick="window.print()" style="background: #4472C4; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px; cursor: pointer;">🖨️ Imprimer</button>
        <button onclick="remplirDonneesExemple()" style="background: #28a745; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px; cursor: pointer;">📊 Remplir Exemple</button>
        <button onclick="viderTableau()" style="background: #dc3545; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px; cursor: pointer;">🗑️ Vider</button>
        <button onclick="document.getElementById('logoUpload').click()" style="background: #ff6b35; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px; cursor: pointer;">🖼️ Changer Logo</button>
        <button onclick="restaurerLogoDefaut()" style="background: #6c757d; color: white; padding: 10px 20px; border: none; border-radius: 5px; margin: 5px; cursor: pointer;">↩️ Logo Défaut</button>
    </div>

    <script>
        // Mettre à jour la date actuelle
        document.getElementById('currentDate').textContent = new Date().toLocaleDateString('fr-FR');
        
        // Fonction pour remplir avec des données d'exemple
        function remplirDonneesExemple() {
            const donneesExemple = [
                {num: 1, ref: "CS2451793", nom: "BEN SEGHIR ABDERAHMANE", articles: "TELE REALME C55 8/256", date: "24/09/2024", prix: "48000.00", totalPaye: "28800.00", echeance: "4800.00"},
                {num: 2, ref: "CS2451796", nom: "SIGA MOHAMMED", articles: "REF 580L+TV 40'' VDR+TENDEUSE", date: "25/09/2024", prix: "142500.00", totalPaye: "85740.00", echeance: "14250.00"},
                {num: 3, ref: "CS2451805", nom: "CHAABNA SAID", articles: "MEUBLES", date: "05/10/2024", prix: "22800.00", totalPaye: "13680.00", echeance: "2280.00"},
                {num: 4, ref: "CS2451807", nom: "BEKKAYE ABDELBAKI", articles: "RADIATEUR A GAZ FG116", date: "06/10/2024", prix: "33000.00", totalPaye: "19800.00", echeance: "3300.00"},
                {num: 5, ref: "CS2451814", nom: "MAZHOUB KHAIRA", articles: "M LAVER 10.5KG RAYLAN +TV 43''", date: "07/10/2024", prix: "158000.00", totalPaye: "94800.00", echeance: "15800.00"},
                {num: 6, ref: "CS2451810", nom: "KOUIDER FATEH", articles: "CUISINIER 4 FEUX ENEIM", date: "07/10/2024", prix: "69000.00", totalPaye: "20700.00", echeance: "6900.00"},
                {num: 7, ref: "CS2451811", nom: "DJAMED ELHOUCINE", articles: "TV 43'' GOOGLE TV STREAM", date: "07/10/2024", prix: "61000.00", totalPaye: "36600.00", echeance: "6100.00"},
                {num: 8, ref: "CS2451822", nom: "DJIREB AMINA", articles: "CHAUF-EAU ELF+RADIATEUR GAZ", date: "09/10/2024", prix: "49750.00", totalPaye: "29850.00", echeance: "4975.00"},
                {num: 9, ref: "CS2451830", nom: "HAMDANI HICHAM", articles: "RADIATEUR GAZ 14KW +TENDEUSE", date: "12/10/2024", prix: "55300.00", totalPaye: "33180.00", echeance: "5530.00"},
                {num: 10, ref: "CS2451832", nom: "DZIRI MOHAMED", articles: "MICRO ONDES 23 L", date: "14/10/2024", prix: "24000.00", totalPaye: "2400.00", echeance: "2400.00"},
                {num: 11, ref: "CS2451833", nom: "AHMED BELKACEM", articles: "REFRIGERATEUR 350L", date: "15/10/2024", prix: "85000.00", totalPaye: "51000.00", echeance: "8500.00"},
                {num: 12, ref: "CS2451834", nom: "FATIMA ZOHRA", articles: "MACHINE A LAVER 8KG", date: "16/10/2024", prix: "75000.00", totalPaye: "45000.00", echeance: "7500.00"},
                {num: 13, ref: "CS2451835", nom: "MOHAMED AMINE", articles: "CLIMATISEUR 18000 BTU", date: "17/10/2024", prix: "95000.00", totalPaye: "57000.00", echeance: "9500.00"},
                {num: 14, ref: "CS2451836", nom: "KHADIJA BENALI", articles: "CUISINIERE GAZ 5 FEUX", date: "18/10/2024", prix: "45000.00", totalPaye: "27000.00", echeance: "4500.00"},
                {num: 15, ref: "CS2451837", nom: "YOUCEF MANSOURI", articles: "TV LED 55 POUCES", date: "19/10/2024", prix: "120000.00", totalPaye: "72000.00", echeance: "12000.00"},
                {num: 16, ref: "CS2451838", nom: "AICHA BENAISSA", articles: "LAVE VAISSELLE 12 COUVERTS", date: "20/10/2024", prix: "65000.00", totalPaye: "39000.00", echeance: "6500.00"},
                {num: 17, ref: "CS2451839", nom: "KARIM BOUDJEMA", articles: "ASPIRATEUR SANS SAC", date: "21/10/2024", prix: "25000.00", totalPaye: "15000.00", echeance: "2500.00"},
                {num: 18, ref: "CS2451840", nom: "SAMIRA CHERIF", articles: "FOUR MICRO ONDES 30L", date: "22/10/2024", prix: "35000.00", totalPaye: "21000.00", echeance: "3500.00"},
                {num: 19, ref: "CS2451841", nom: "RACHID HAMIDI", articles: "CONGELATEUR 200L", date: "23/10/2024", prix: "55000.00", totalPaye: "33000.00", echeance: "5500.00"},
                {num: 20, ref: "CS2451842", nom: "NADIA SALEM", articles: "MACHINE A CAFE EXPRESSO", date: "24/10/2024", prix: "18000.00", totalPaye: "10800.00", echeance: "1800.00"},
                {num: 21, ref: "CS2451843", nom: "OMAR BRAHIM", articles: "VENTILATEUR PLAFOND", date: "25/10/2024", prix: "12000.00", totalPaye: "7200.00", echeance: "1200.00"},
                {num: 22, ref: "CS2451844", nom: "LEILA MOKRANI", articles: "RADIATEUR ELECTRIQUE", date: "26/10/2024", prix: "28000.00", totalPaye: "16800.00", echeance: "2800.00"},
                {num: 23, ref: "CS2451845", nom: "HASSAN BENALI", articles: "CHAUFFE EAU ELECTRIQUE 80L", date: "27/10/2024", prix: "42000.00", totalPaye: "25200.00", echeance: "4200.00"},
                {num: 24, ref: "CS2451846", nom: "ZINEB KADRI", articles: "MIXEUR PLONGEANT", date: "28/10/2024", prix: "8000.00", totalPaye: "4800.00", echeance: "800.00"},
                {num: 25, ref: "CS2451847", nom: "ABDERRAHIM TOUATI", articles: "GRILLE PAIN 4 TRANCHES", date: "29/10/2024", prix: "15000.00", totalPaye: "9000.00", echeance: "1500.00"},
                {num: 26, ref: "CS2451848", nom: "MALIKA FERHAT", articles: "BOUILLOIRE ELECTRIQUE", date: "30/10/2024", prix: "6000.00", totalPaye: "3600.00", echeance: "600.00"},
                {num: 27, ref: "CS2451849", nom: "SAID BENCHEIKH", articles: "ROBOT CUISINE MULTIFONCTION", date: "31/10/2024", prix: "38000.00", totalPaye: "22800.00", echeance: "3800.00"},
                {num: 28, ref: "CS2451850", nom: "FARIDA AMRANI", articles: "SECHE CHEVEUX PROFESSIONNEL", date: "01/11/2024", prix: "9500.00", totalPaye: "5700.00", echeance: "950.00"},
                {num: 29, ref: "CS2451851", nom: "MUSTAPHA ZIANI", articles: "FER A REPASSER VAPEUR", date: "02/11/2024", prix: "11000.00", totalPaye: "6600.00", echeance: "1100.00"},
                {num: 30, ref: "CS2451852", nom: "HOURIA BENHADJ", articles: "BALANCE DE CUISINE DIGITALE", date: "03/11/2024", prix: "4500.00", totalPaye: "2700.00", echeance: "450.00"}
            ];
            
            remplirTableau(donneesExemple);
        }
        
        // Fonction pour remplir le tableau
        function remplirTableau(donnees) {
            const tbody = document.getElementById('factureTableBody');
            tbody.innerHTML = '';
            
            let totalPrix = 0;
            let totalPaye = 0;
            
            donnees.forEach(item => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td>${item.num}</td>
                    <td class="text-left">${item.ref}</td>
                    <td class="text-left">${item.nom}</td>
                    <td class="text-left">${item.articles}</td>
                    <td>${item.date}</td>
                    <td class="text-right">${item.prix}</td>
                    <td class="text-right">${item.totalPaye}</td>
                    <td class="text-right">${item.echeance}</td>
                `;
                
                totalPrix += parseFloat(item.prix);
                totalPaye += parseFloat(item.totalPaye);
            });
            
            // Ligne total
            const totalRow = tbody.insertRow();
            totalRow.className = 'total-row';
            totalRow.innerHTML = `
                <td colspan="5"><strong>TOTAL</strong></td>
                <td class="text-right"><strong>${totalPrix.toFixed(2)}</strong></td>
                <td class="text-right"><strong>${totalPaye.toFixed(2)}</strong></td>
                <td class="text-right"><strong>${(totalPrix - totalPaye).toFixed(2)}</strong></td>
            `;

            // Mettre à jour le montant en lettres
            setTimeout(() => {
                mettreAJourMontantEnLettres();
            }, 100);
        }
        
        // Fonction pour vider le tableau
        function viderTableau() {
            document.getElementById('factureTableBody').innerHTML = '';
        }
        
        // Fonction pour charger les données depuis localStorage (du gestionnaire principal)
        function chargerDonneesGestionnaire() {
            const commandesData = localStorage.getItem('commandesData');
            if (commandesData) {
                const commandes = JSON.parse(commandesData);
                const donneesFacture = commandes.map((cmd, index) => ({
                    num: index + 1,
                    ref: cmd.refClient,
                    nom: cmd.nomPrenom,
                    articles: cmd.articles,
                    date: cmd.dateCommande,
                    prix: cmd.prix.toFixed(2),
                    totalPaye: cmd.totalPaye.toFixed(2),
                    echeance: (cmd.prix / 10).toFixed(2) // Échéance = prix / 10
                }));
                remplirTableau(donneesFacture);
            } else {
                // Si pas de données, charger l'exemple
                remplirDonneesExemple();
            }
        }

        // Fonction pour convertir nombre en lettres (français)
        function nombreEnLettres(nombre) {
            if (nombre === 0) return 'zéro';

            const unites = ['', 'un', 'deux', 'trois', 'quatre', 'cinq', 'six', 'sept', 'huit', 'neuf',
                           'dix', 'onze', 'douze', 'treize', 'quatorze', 'quinze', 'seize', 'dix-sept', 'dix-huit', 'dix-neuf'];

            const dizaines = ['', '', 'vingt', 'trente', 'quarante', 'cinquante', 'soixante', 'soixante-dix', 'quatre-vingt', 'quatre-vingt-dix'];

            function convertirGroupe(n) {
                let resultat = '';

                // Centaines
                const centaines = Math.floor(n / 100);
                if (centaines > 0) {
                    if (centaines === 1) {
                        resultat += 'cent';
                    } else {
                        resultat += unites[centaines] + ' cent';
                        if (n % 100 === 0) resultat += 's'; // cents
                    }
                }

                // Dizaines et unités
                const reste = n % 100;
                if (reste > 0) {
                    if (resultat) resultat += ' ';

                    if (reste < 20) {
                        resultat += unites[reste];
                    } else {
                        const diz = Math.floor(reste / 10);
                        const unit = reste % 10;

                        if (diz === 7 || diz === 9) {
                            // Cas spéciaux: 70-79 et 90-99
                            if (diz === 7) {
                                resultat += 'soixante';
                                if (unit === 1) resultat += ' et onze';
                                else if (unit > 1) resultat += '-' + unites[10 + unit];
                                else resultat += '-dix';
                            } else { // diz === 9
                                resultat += 'quatre-vingt';
                                if (unit === 1) resultat += ' et onze';
                                else if (unit > 1) resultat += '-' + unites[10 + unit];
                                else resultat += '-dix';
                            }
                        } else if (diz === 8) {
                            resultat += 'quatre-vingt';
                            if (unit === 0) resultat += 's';
                            else resultat += '-' + unites[unit];
                        } else {
                            resultat += dizaines[diz];
                            if (unit === 1 && diz !== 8) resultat += ' et un';
                            else if (unit > 1) resultat += '-' + unites[unit];
                        }
                    }
                }

                return resultat;
            }

            // Traitement des millions
            if (nombre >= 1000000) {
                const millions = Math.floor(nombre / 1000000);
                const reste = nombre % 1000000;
                let resultat = '';

                if (millions === 1) {
                    resultat = 'un million';
                } else {
                    resultat = convertirGroupe(millions) + ' millions';
                }

                if (reste > 0) {
                    resultat += ' ' + nombreEnLettres(reste);
                }

                return resultat;
            }

            // Traitement des milliers
            if (nombre >= 1000) {
                const milliers = Math.floor(nombre / 1000);
                const reste = nombre % 1000;
                let resultat = '';

                if (milliers === 1) {
                    resultat = 'mille';
                } else {
                    resultat = convertirGroupe(milliers) + ' mille';
                }

                if (reste > 0) {
                    resultat += ' ' + convertirGroupe(reste);
                }

                return resultat;
            }

            return convertirGroupe(nombre);
        }

        // Fonction pour mettre à jour le montant en lettres
        function mettreAJourMontantEnLettres() {
            const rows = document.querySelectorAll('#factureTableBody tr:not(.total-row)');
            let totalPrix = 0;

            rows.forEach(row => {
                const prixCell = row.cells[5]; // Colonne PRIX
                if (prixCell) {
                    const prix = parseFloat(prixCell.textContent.replace(/[^\d.-]/g, ''));
                    if (!isNaN(prix)) {
                        totalPrix += prix;
                    }
                }
            });

            const montantEnLettres = nombreEnLettres(Math.floor(totalPrix));
            const montantTexteElement = document.getElementById('montantTexte');
            if (montantTexteElement) {
                montantTexteElement.textContent = montantEnLettres;
            }
        }

        // Fonction supprimée - plus de texte automatique

        // Fonction pour changer le logo
        function changerLogo(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    const logoImage = document.getElementById('logoImage');
                    const logoText = document.getElementById('logoText');

                    logoImage.src = e.target.result;
                    logoImage.style.display = 'block';
                    logoText.style.display = 'none';

                    // Sauvegarder l'image dans localStorage
                    localStorage.setItem('logoFacture', e.target.result);
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // Fonction pour restaurer le logo par défaut
        function restaurerLogoDefaut() {
            const logoImage = document.getElementById('logoImage');
            const logoText = document.getElementById('logoText');

            logoImage.style.display = 'none';
            logoText.style.display = 'block';

            // Supprimer de localStorage
            localStorage.removeItem('logoFacture');
        }

        // Fonction pour charger le logo sauvegardé
        function chargerLogoSauvegarde() {
            const logoSauvegarde = localStorage.getItem('logoFacture');
            if (logoSauvegarde) {
                const logoImage = document.getElementById('logoImage');
                const logoText = document.getElementById('logoText');

                logoImage.src = logoSauvegarde;
                logoImage.style.display = 'block';
                logoText.style.display = 'none';
            }
        }

        // Fonction supprimée

        // Charger les données au démarrage
        chargerDonneesGestionnaire();
        chargerLogoSauvegarde();

        // Mettre à jour le montant en lettres au démarrage
        setTimeout(() => {
            mettreAJourMontantEnLettres();
        }, 500);
    </script>
</body>
</html>
