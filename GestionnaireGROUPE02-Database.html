<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestionnaire de Commandes Clients - GROUPE 02 (Database)</title>
    <!-- Bibliothèque SheetJS pour Excel -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f0f0;
            color: #333;
        }

        .container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #4682b4, #5a9fd4);
            color: white;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .header h1 {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
        }

        .database-indicator {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            margin-top: 5px;
            display: inline-block;
        }

        .toolbar {
            background-color: #e6e6e6;
            padding: 10px;
            border-bottom: 1px solid #ccc;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid #999;
            background: linear-gradient(to bottom, #f8f8f8, #e0e0e0);
            color: #333;
            cursor: pointer;
            font-size: 12px;
            border-radius: 3px;
            transition: all 0.2s;
        }

        .btn:hover {
            background: linear-gradient(to bottom, #e8e8e8, #d0d0d0);
            border-color: #666;
        }

        .btn:active {
            background: linear-gradient(to bottom, #d0d0d0, #e8e8e8);
            box-shadow: inset 1px 1px 3px rgba(0,0,0,0.2);
        }

        .btn-primary { background: linear-gradient(to bottom, #4682b4, #2e5984); color: white; border-color: #2e5984; }
        .btn-danger { background: linear-gradient(to bottom, #dc143c, #b91c3c); color: white; border-color: #b91c3c; }
        .btn-success { background: linear-gradient(to bottom, #228b22, #1e7e1e); color: white; border-color: #1e7e1e; }

        .table-container {
            flex: 1;
            overflow: auto;
            background: white;
            border: 1px solid #ccc;
            margin: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }

        th {
            background: linear-gradient(to bottom, #4682b4, #2e5984);
            color: white;
            padding: 8px 4px;
            text-align: center;
            border: 1px solid #2e5984;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        td {
            padding: 6px 4px;
            border: 1px solid #ddd;
            text-align: center;
        }

        tr:nth-child(even) {
            background-color: #f8f8f8;
        }

        tr:hover {
            background-color: #add8e6;
        }

        tr.selected {
            background-color: #87ceeb !important;
        }

        .status-checkbox {
            width: 16px;
            height: 16px;
        }

        .status-col {
            width: 25px;
        }

        .num-col { 
            width: 75px; 
            min-width: 75px;
            max-width: 75px;
        }
        .ref-col { width: 100px; }
        .nom-col { width: 200px; }
        .articles-col { width: 250px; }
        .date-col { width: 100px; }
        .prix-col { width: 100px; }
        .total-col { width: 100px; }

        .loading {
            text-align: center;
            padding: 20px;
            color: #666;
        }

        .error {
            background-color: #f8d7da;
            color: #721c24;
            padding: 10px;
            margin: 10px;
            border-radius: 5px;
            border: 1px solid #f5c6cb;
        }

        .success {
            background-color: #d4edda;
            color: #155724;
            padding: 10px;
            margin: 10px;
            border-radius: 5px;
            border: 1px solid #c3e6cb;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #f0f0f0;
            margin: 5% auto;
            padding: 0;
            border: 2px solid #999;
            width: 500px;
            border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(to bottom, #4682b4, #2e5984);
            color: white;
            padding: 10px 15px;
            border-bottom: 1px solid #2e5984;
        }

        .modal-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group input, .form-group textarea {
            width: 100%;
            padding: 6px;
            border: 1px solid #999;
            border-radius: 3px;
            font-size: 12px;
        }

        .form-group textarea {
            height: 60px;
            resize: vertical;
        }

        .modal-footer {
            padding: 15px;
            text-align: right;
            border-top: 1px solid #ddd;
        }

        .close {
            color: white;
            float: right;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            opacity: 0.7;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 id="titreGroupe">GESTIONNAIRE DE COMMANDES CLIENTS - GROUPE 02</h1>
            <div class="database-indicator">
                🗄️ متصل بقاعدة البيانات
            </div>
            <div style="margin-top: 10px;">
                <button class="btn" onclick="retournerGroupes()" style="background: linear-gradient(to bottom, #6c757d, #5a6268); color: white; border-color: #6c757d;">
                    ← Retour aux Groupes
                </button>
            </div>
        </div>
        
        <div class="toolbar">
            <button class="btn btn-primary" onclick="ouvrirModalAjout()">Ajouter Commande</button>
            <button class="btn btn-danger" onclick="supprimerCommande()">Supprimer</button>
            <button class="btn btn-success" onclick="actualiserDonnees()">Actualiser</button>
            <button class="btn" onclick="exporterExcel()" style="background: linear-gradient(to bottom, #228b22, #32cd32); color: white; border-color: #228b22;">📊 Exporter Excel</button>

            <!-- Contrôle du nombre de colonnes par défaut -->
            <div style="display: flex; align-items: center; gap: 10px; margin-left: 20px;">
                <label for="nombreColonnes" style="font-weight: bold; color: #2e5984;">Colonnes par défaut:</label>
                <input type="number" id="nombreColonnes" value="10" min="1" max="20" style="width: 60px; padding: 4px; border: 1px solid #999; border-radius: 3px; text-align: center;">
            </div>

            <!-- Statistiques -->
            <div style="margin-left: auto; display: flex; gap: 20px; align-items: center; font-weight: bold; color: #2e5984;">
                <span id="totalGeneral">TOTAL GÉNÉRAL: 0.00 DA</span>
                <span id="totalPaye">TOTAL PAYÉ: 0.00 DA</span>
                <span id="totalRestant">RESTANT: 0.00 DA</span>
            </div>
        </div>

        <div id="messageContainer"></div>

        <div class="table-container">
            <div id="loadingIndicator" class="loading">
                🔄 تحميل البيانات من قاعدة البيانات...
            </div>
            <table id="tableCommandes" style="display: none;">
                <thead>
                    <tr id="headerRow">
                        <th class="num-col">N°</th>
                        <th class="ref-col">REF CLI</th>
                        <th class="nom-col">NOM ET PRÉNOM</th>
                        <th class="articles-col">ARTICLES COM</th>
                        <th class="date-col">DATE DE COM</th>
                        <th class="prix-col">PRIX</th>
                        <th class="total-col">TOTAL PAYÉ</th>
                        <th class="total-col">RESTANT</th>
                        <!-- Les colonnes de statut seront ajoutées dynamiquement -->
                    </tr>
                </thead>
                <tbody id="corpsTable">
                </tbody>
            </table>
        </div>
    </div>

    <!-- Modal d'ajout -->
    <div id="modalAjout" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="close" onclick="fermerModal()">&times;</span>
                <h3>Ajouter une Nouvelle Commande</h3>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="refClient">Référence Client:</label>
                    <input type="text" id="refClient" placeholder="Ex: OS24S1793">
                </div>
                <div class="form-group">
                    <label for="nomPrenom">Nom et Prénom:</label>
                    <input type="text" id="nomPrenom" placeholder="Ex: BEN SEGHIR ABDERAHMANE">
                </div>
                <div class="form-group">
                    <label for="articles">Articles:</label>
                    <textarea id="articles" placeholder="Description des articles commandés"></textarea>
                </div>
                <div class="form-group">
                    <label for="dateCommande">Date de Commande:</label>
                    <input type="date" id="dateCommande">
                </div>
                <div class="form-group">
                    <label for="prix">Prix:</label>
                    <input type="number" id="prix" step="0.01" placeholder="0.00">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-success" onclick="sauvegarderCommande()">Sauvegarder</button>
                <button class="btn" onclick="fermerModal()">Annuler</button>
            </div>
        </div>
    </div>

    <script>
        // Configuration de l'API
        const API_BASE_URL = 'http://localhost:8080/api';
        
        let commandes = [];
        let ligneSelectionnee = null;
        let nombreColonnesDefaut = 10;

        // Fonctions utilitaires pour l'API
        async function apiRequest(endpoint, options = {}) {
            try {
                const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                return await response.json();
            } catch (error) {
                console.error('خطأ في API:', error);
                afficherMessage(`خطأ في الاتصال: ${error.message}`, 'error');
                throw error;
            }
        }

        function afficherMessage(message, type = 'info') {
            const container = document.getElementById('messageContainer');
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;
            container.appendChild(messageDiv);

            // إزالة الرسالة بعد 5 ثوان
            setTimeout(() => {
                container.removeChild(messageDiv);
            }, 5000);
        }

        async function chargerDonnees() {
            try {
                document.getElementById('loadingIndicator').style.display = 'block';
                document.getElementById('tableCommandes').style.display = 'none';

                const response = await apiRequest('/commandes');
                commandes = response.data;

                creerColonnesStatut();
                afficherCommandes();
                mettreAJourStatistiques();

                document.getElementById('loadingIndicator').style.display = 'none';
                document.getElementById('tableCommandes').style.display = 'table';

                console.log(`✅ تم تحميل ${commandes.length} طلب من قاعدة البيانات`);
            } catch (error) {
                document.getElementById('loadingIndicator').innerHTML =
                    `❌ فشل في تحميل البيانات: ${error.message}<br><button onclick="chargerDonnees()" class="btn btn-primary">إعادة المحاولة</button>`;
            }
        }

        function creerColonnesStatut() {
            const headerRow = document.getElementById('headerRow');
            const existingStatusHeaders = headerRow.querySelectorAll('.status-header');
            existingStatusHeaders.forEach(header => header.remove());

            const maxColonnes = Math.max(...commandes.map(cmd => cmd.nombreColonnes || 10), nombreColonnesDefaut);

            for (let i = 1; i <= maxColonnes; i++) {
                const th = document.createElement('th');
                th.className = 'status-col status-header';
                th.textContent = i.toString();
                headerRow.appendChild(th);
            }
        }

        function afficherCommandes() {
            const tbody = document.getElementById('corpsTable');
            tbody.innerHTML = '';

            const maxColonnes = Math.max(...commandes.map(cmd => cmd.nombreColonnes || 10), nombreColonnesDefaut);

            commandes.forEach(commande => {
                const tr = document.createElement('tr');
                tr.onclick = () => selectionnerLigne(tr, commande.id);

                if (!commande.nombreColonnes) {
                    commande.nombreColonnes = 10;
                }

                let statutsHtml = '';
                for (let i = 0; i < maxColonnes; i++) {
                    if (i < commande.nombreColonnes) {
                        const isChecked = i < commande.statuts.length ? commande.statuts[i] : 0;
                        statutsHtml += `<td><input type="checkbox" class="status-checkbox" ${isChecked ? 'checked' : ''} onchange="changerStatut(${commande.id}, ${i}, this.checked)"></td>`;
                    } else {
                        statutsHtml += `<td style="background-color: #f5f5f5;"></td>`;
                    }
                }

                const restant = commande.prix - commande.totalPaye;

                const controlColonnes = `
                    <div style="display: flex; align-items: center; gap: 3px; font-size: 11px; margin-top: 4px;">
                        <input type="number" value="${commande.nombreColonnes}" min="1" max="20"
                               style="width: 45px; padding: 3px; font-size: 11px; text-align: center; border: 1px solid #999; border-radius: 3px; font-weight: bold;"
                               onchange="changerNombreColonnesCommande(${commande.id}, this.value)"
                               title="عدد الأعمدة لهذا الطلب">
                        <span style="color: #666; font-size: 10px; font-weight: bold;">أعمدة</span>
                    </div>
                `;

                tr.innerHTML = `
                    <td class="num-col" style="text-align: center; vertical-align: middle; padding: 4px;">
                        <div style="font-weight: bold; font-size: 14px; color: #2e5984;">${commande.id}</div>
                        ${controlColonnes}
                    </td>
                    <td>${commande.refClient}</td>
                    <td>${commande.nomPrenom}</td>
                    <td>${commande.articles}</td>
                    <td>${commande.dateCommande}</td>
                    <td style="font-weight: bold;">${commande.prix.toFixed(2)}</td>
                    <td style="color: ${commande.totalPaye > 0 ? '#228b22' : '#666'}; font-weight: bold;">${commande.totalPaye.toFixed(2)}</td>
                    <td style="color: ${restant > 0 ? '#dc143c' : '#228b22'}; font-weight: bold;">${restant.toFixed(2)}</td>
                    ${statutsHtml}
                `;

                tbody.appendChild(tr);
            });
        }

        function mettreAJourStatistiques() {
            const totalGeneral = commandes.reduce((sum, cmd) => sum + cmd.prix, 0);
            const totalPaye = commandes.reduce((sum, cmd) => sum + cmd.totalPaye, 0);
            const totalRestant = totalGeneral - totalPaye;

            document.getElementById('totalGeneral').textContent = `TOTAL GÉNÉRAL: ${totalGeneral.toFixed(2)} DA`;
            document.getElementById('totalPaye').textContent = `TOTAL PAYÉ: ${totalPaye.toFixed(2)} DA`;
            document.getElementById('totalRestant').textContent = `RESTANT: ${totalRestant.toFixed(2)} DA`;

            const elementRestant = document.getElementById('totalRestant');
            if (totalRestant <= 0) {
                elementRestant.style.color = '#228b22';
            } else {
                elementRestant.style.color = '#dc143c';
            }
        }

        function selectionnerLigne(tr, id) {
            document.querySelectorAll('tr').forEach(row => row.classList.remove('selected'));
            tr.classList.add('selected');
            ligneSelectionnee = id;
        }

        async function changerStatut(id, index, estCoche) {
            try {
                await apiRequest('/statuts', {
                    method: 'PUT',
                    body: JSON.stringify({
                        id: id,
                        index: index,
                        value: estCoche
                    })
                });

                // إعادة تحميل البيانات لإظهار التحديثات
                await chargerDonnees();
                afficherMessage('تم تحديث الحالة بنجاح', 'success');
            } catch (error) {
                // إعادة تعيين الحالة في حالة الفشل
                const checkbox = event.target;
                checkbox.checked = !estCoche;
                afficherMessage('فشل في تحديث الحالة', 'error');
            }
        }

        async function changerNombreColonnesCommande(id, nouveauNombre) {
            const nombre = parseInt(nouveauNombre);

            if (isNaN(nombre) || nombre < 1 || nombre > 20) {
                afficherMessage('يرجى إدخال رقم بين 1 و 20', 'error');
                await chargerDonnees();
                return;
            }

            try {
                await apiRequest('/commandes', {
                    method: 'PUT',
                    body: JSON.stringify({
                        id: id,
                        nombreColonnes: nombre
                    })
                });

                await chargerDonnees();
                afficherMessage(`تم تغيير عدد الأعمدة للطلب ${id} إلى ${nombre}`, 'success');
            } catch (error) {
                afficherMessage('فشل في تحديث عدد الأعمدة', 'error');
                await chargerDonnees();
            }
        }

        function ouvrirModalAjout() {
            document.getElementById('modalAjout').style.display = 'block';
            document.getElementById('dateCommande').value = new Date().toISOString().split('T')[0];
        }

        function fermerModal() {
            document.getElementById('modalAjout').style.display = 'none';
            document.getElementById('refClient').value = '';
            document.getElementById('nomPrenom').value = '';
            document.getElementById('articles').value = '';
            document.getElementById('prix').value = '';
        }

        async function sauvegarderCommande() {
            const refClient = document.getElementById('refClient').value.trim();
            const nomPrenom = document.getElementById('nomPrenom').value.trim();
            const articles = document.getElementById('articles').value.trim();
            const dateCommande = document.getElementById('dateCommande').value;
            const prix = parseFloat(document.getElementById('prix').value) || 0;

            if (!refClient || !nomPrenom || !articles || prix <= 0) {
                afficherMessage('يرجى ملء جميع الحقول المطلوبة وإدخال سعر صحيح', 'error');
                return;
            }

            try {
                await apiRequest('/commandes', {
                    method: 'POST',
                    body: JSON.stringify({
                        refClient,
                        nomPrenom,
                        articles,
                        dateCommande: new Date(dateCommande).toLocaleDateString('fr-FR'),
                        prix,
                        totalPaye: 0
                    })
                });

                await chargerDonnees();
                fermerModal();
                afficherMessage('تم إضافة الطلب بنجاح!', 'success');
            } catch (error) {
                afficherMessage('فشل في إضافة الطلب', 'error');
            }
        }

        async function supprimerCommande() {
            if (!ligneSelectionnee) {
                afficherMessage('يرجى اختيار طلب للحذف', 'error');
                return;
            }

            if (confirm('هل أنت متأكد من حذف هذا الطلب؟')) {
                try {
                    await apiRequest(`/commandes?id=${ligneSelectionnee}`, {
                        method: 'DELETE'
                    });

                    await chargerDonnees();
                    ligneSelectionnee = null;
                    afficherMessage('تم حذف الطلب بنجاح!', 'success');
                } catch (error) {
                    afficherMessage('فشل في حذف الطلب', 'error');
                }
            }
        }

        async function actualiserDonnees() {
            await chargerDonnees();
            afficherMessage('تم تحديث البيانات!', 'success');
        }

        function exporterExcel() {
            // نفس كود التصدير السابق
            afficherMessage('ميزة التصدير ستكون متاحة قريباً', 'info');
        }

        function retournerGroupes() {
            window.location.href = 'GestionnaireGroupes.html';
        }

        // Fermer le modal en cliquant à l'extérieur
        window.onclick = function(event) {
            const modal = document.getElementById('modalAjout');
            if (event.target === modal) {
                fermerModal();
            }
        }

        // تحميل البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('nombreColonnes').value = nombreColonnesDefaut;
            chargerDonnees();
        });
    </script>
</body>
</html>
