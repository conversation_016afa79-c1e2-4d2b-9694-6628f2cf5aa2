using System;
using System.Data;
using System.Data.SQLite;
using System.IO;

namespace CustomerOrderManager
{
    public class DatabaseManager
    {
        private string connectionString;
        private string dbPath;

        public DatabaseManager()
        {
            dbPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "orders.db");
            connectionString = $"Data Source={dbPath};Version=3;";
            InitializeDatabase();
        }

        private void InitializeDatabase()
        {
            if (!File.Exists(dbPath))
            {
                SQLiteConnection.CreateFile(dbPath);
            }

            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                
                // Création de la table des commandes clients
                string createTableQuery = @"
                    CREATE TABLE IF NOT EXISTS Commandes (
                        Id INTEGER PRIMARY KEY AUTOINCREMENT,
                        RefClient TEXT NOT NULL,
                        NomPrenom TEXT NOT NULL,
                        Articles TEXT NOT NULL,
                        DateCommande TEXT NOT NULL,
                        Prix REAL NOT NULL,
                        TotalPaye REAL NOT NULL,
                        Statut1 INTEGER DEFAULT 0,
                        Statut2 INTEGER DEFAULT 0,
                        Statut3 INTEGER DEFAULT 0,
                        Statut4 INTEGER DEFAULT 0,
                        Statut5 INTEGER DEFAULT 0,
                        Statut6 INTEGER DEFAULT 0,
                        Statut7 INTEGER DEFAULT 0,
                        Statut8 INTEGER DEFAULT 0,
                        Statut9 INTEGER DEFAULT 0,
                        Statut10 INTEGER DEFAULT 0,
                        DateCreation TEXT DEFAULT CURRENT_TIMESTAMP
                    )";
                
                using (var command = new SQLiteCommand(createTableQuery, connection))
                {
                    command.ExecuteNonQuery();
                }

                // Ajouter des données d'exemple si la table est vide
                AjouterDonneesExemple(connection);
            }
        }

        public DataTable GetToutesCommandes()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = @"SELECT *,
                    (Prix - TotalPaye) AS Restant
                    FROM Commandes ORDER BY Id DESC";
                using (var adapter = new SQLiteDataAdapter(query, connection))
                {
                    DataTable dataTable = new DataTable();
                    adapter.Fill(dataTable);
                    return dataTable;
                }
            }
        }

        public void AjouterCommande(string refClient, string nomPrenom, string articles,
                                  string dateCommande, decimal prix, decimal totalPaye)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = @"INSERT INTO Commandes
                    (RefClient, NomPrenom, Articles, DateCommande, Prix, TotalPaye)
                    VALUES (@refClient, @nomPrenom, @articles, @dateCommande, @prix, @totalPaye)";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@refClient", refClient);
                    command.Parameters.AddWithValue("@nomPrenom", nomPrenom);
                    command.Parameters.AddWithValue("@articles", articles);
                    command.Parameters.AddWithValue("@dateCommande", dateCommande);
                    command.Parameters.AddWithValue("@prix", prix);
                    command.Parameters.AddWithValue("@totalPaye", totalPaye);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void MettreAJourStatut(int commandeId, int colonneStatut, bool estCoche, int nombreColonnes = 10)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();

                // Mettre à jour le statut
                string updateStatusQuery = $"UPDATE Commandes SET Statut{colonneStatut} = @estCoche WHERE Id = @commandeId";
                using (var command = new SQLiteCommand(updateStatusQuery, connection))
                {
                    command.Parameters.AddWithValue("@estCoche", estCoche ? 1 : 0);
                    command.Parameters.AddWithValue("@commandeId", commandeId);
                    command.ExecuteNonQuery();
                }

                // Calculer automatiquement le Total Payé selon la formule Excel avec nombre de colonnes variable
                string calculateQuery = $@"
                    UPDATE Commandes
                    SET TotalPaye = (
                        (Statut1 + Statut2 + Statut3 + Statut4 + Statut5 +
                         Statut6 + Statut7 + Statut8 + Statut9 + Statut10) * Prix / {nombreColonnes}.0
                    )
                    WHERE Id = @commandeId";

                using (var command = new SQLiteCommand(calculateQuery, connection))
                {
                    command.Parameters.AddWithValue("@commandeId", commandeId);
                    command.ExecuteNonQuery();
                }
            }
        }

        public void SupprimerCommande(int commandeId)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();
                string query = "DELETE FROM Commandes WHERE Id = @commandeId";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.Parameters.AddWithValue("@commandeId", commandeId);
                    command.ExecuteNonQuery();
                }
            }
        }

        private void AjouterDonneesExemple(SQLiteConnection connection)
        {
            // Vérifier si des données existent déjà
            string checkQuery = "SELECT COUNT(*) FROM Commandes";
            using (var checkCommand = new SQLiteCommand(checkQuery, connection))
            {
                int count = Convert.ToInt32(checkCommand.ExecuteScalar());
                if (count > 0) return; // Des données existent déjà
            }

            // Ajouter des données d'exemple avec statuts prédéfinis
            string[] donneesExemple = {
                "INSERT INTO Commandes (RefClient, NomPrenom, Articles, DateCommande, Prix, TotalPaye, Statut1, Statut2, Statut3, Statut4, Statut5, Statut6) VALUES ('OS24S1793', 'BEN SEGHIR ABDERAHMANE', 'TELE REALME C65 8/256', '24/09/2024', 48000.00, 28800.00, 1, 1, 1, 1, 1, 1)",
                "INSERT INTO Commandes (RefClient, NomPrenom, Articles, DateCommande, Prix, TotalPaye, Statut1, Statut2, Statut3, Statut4, Statut5, Statut6) VALUES ('OS24S1796', 'SIGA MOHAMMED', 'REF 580L+ TV 40\" VDDA+ TENDEUSE', '29/09/2024', 142900.00, 85740.00, 1, 1, 1, 1, 1, 1)",
                "INSERT INTO Commandes (RefClient, NomPrenom, Articles, DateCommande, Prix, TotalPaye, Statut1, Statut2, Statut3, Statut4, Statut5, Statut6) VALUES ('OS24S1805', 'CHABIRA IBRAHIM', 'MEUBLES', '05/10/2024', 22800.00, 13680.00, 1, 1, 1, 1, 1, 1)",
                "INSERT INTO Commandes (RefClient, NomPrenom, Articles, DateCommande, Prix, TotalPaye, Statut1, Statut2, Statut3, Statut4, Statut5, Statut6) VALUES ('OS24S1807', 'BEKKAYE ABDELBAKI', 'RADIATEUR A GAZ FG11G', '06/10/2024', 33000.00, 19800.00, 1, 1, 1, 1, 1, 1)",
                "INSERT INTO Commandes (RefClient, NomPrenom, Articles, DateCommande, Prix, TotalPaye, Statut1, Statut2, Statut3, Statut4, Statut5, Statut6) VALUES ('OS24S1814', 'MAZHOUD KHAIRA', 'M.LAVER 10.5KG RAYLAN + TV 43\" WEBOS', '07/10/2024', 158000.00, 94800.00, 1, 1, 1, 1, 1, 1)",
                "INSERT INTO Commandes (RefClient, NomPrenom, Articles, DateCommande, Prix, TotalPaye, Statut1, Statut2, Statut3) VALUES ('OS24S1810', 'KOURRINI ATTIA', 'CUISINIER 4 FEUX ENEIM', '07/10/2024', 69000.00, 20700.00, 1, 1, 1)",
                "INSERT INTO Commandes (RefClient, NomPrenom, Articles, DateCommande, Prix, TotalPaye, Statut1, Statut2, Statut3, Statut4, Statut5, Statut6) VALUES ('OS24S1811', 'DJAMED ELHOUCINE', 'TV 43\" GOOGLE TV STREAM', '07/10/2024', 61000.00, 36600.00, 1, 1, 1, 1, 1, 1)",
                "INSERT INTO Commandes (RefClient, NomPrenom, Articles, DateCommande, Prix, TotalPaye, Statut1, Statut2, Statut3, Statut4, Statut5, Statut6) VALUES ('OS24S1822', 'DJIREB AMINA', 'CHAUF-EAU 6L+ RADIATEUR GAZ', '09/10/2024', 49750.00, 29850.00, 1, 1, 1, 1, 1, 1)",
                "INSERT INTO Commandes (RefClient, NomPrenom, Articles, DateCommande, Prix, TotalPaye, Statut1, Statut2, Statut3, Statut4, Statut5, Statut6) VALUES ('OS24S1830', 'HAMDANI HICHAM', 'RADIATEUR GAZ 14KW + TENDEUSE', '12/10/2024', 55300.00, 33180.00, 1, 1, 1, 1, 1, 1)",
                "INSERT INTO Commandes (RefClient, NomPrenom, Articles, DateCommande, Prix, TotalPaye, Statut1) VALUES ('OS24S1832', 'DZIRI MOHAMED', 'MICRO ONDES 23 L', '14/10/2024', 24000.00, 2400.00, 1)"
            };

            foreach (string requete in donneesExemple)
            {
                using (var command = new SQLiteCommand(requete, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        public void RecalculerTousLesTotaux()
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();

                // Recalculer tous les totaux payés selon les statuts
                string query = @"
                    UPDATE Commandes
                    SET TotalPaye = (
                        (Statut1 + Statut2 + Statut3 + Statut4 + Statut5 +
                         Statut6 + Statut7 + Statut8 + Statut9 + Statut10) * Prix / 10.0
                    )";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }

        public void MettreAJourNombreColonnes(int nombreColonnes)
        {
            using (var connection = new SQLiteConnection(connectionString))
            {
                connection.Open();

                // Recalculer tous les totaux selon le nouveau nombre de colonnes
                string query = $@"
                    UPDATE Commandes
                    SET TotalPaye = (
                        (Statut1 + Statut2 + Statut3 + Statut4 + Statut5 +
                         Statut6 + Statut7 + Statut8 + Statut9 + Statut10) * Prix / {nombreColonnes}.0
                    )";

                using (var command = new SQLiteCommand(query, connection))
                {
                    command.ExecuteNonQuery();
                }
            }
        }
    }
}
