@echo off
echo ========================================
echo خادم الويب - مدير الطلبات
echo GROUPE 02
echo ========================================
echo.

echo 🔧 بناء التطبيق...
dotnet build --configuration Release

if %ERRORLEVEL% NEQ 0 (
    echo ❌ فشل في بناء التطبيق
    pause
    exit /b 1
)

echo.
echo 🚀 بدء تشغيل خادم الويب...
echo 🌐 سيكون متاحاً على: http://localhost:8080
echo 📊 واجهة الإدارة: http://localhost:8080/GestionnaireGroupes.html
echo.
echo اضغط Ctrl+C لإيقاف الخادم
echo.

dotnet run --configuration Release -- --web

pause
