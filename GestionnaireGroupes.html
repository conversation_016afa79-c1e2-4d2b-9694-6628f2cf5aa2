<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestionnaire de Groupes - DJEMIAL MOUSTAPHA</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #4472C4, #5B9BD5);
            color: white;
            padding: 30px;
            text-align: center;
            position: relative;
        }
        .header h1 {
            font-size: 28px;
            margin-bottom: 10px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        .header p {
            font-size: 16px;
            opacity: 0.9;
        }
        .logo-corner {
            position: absolute;
            top: 20px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: linear-gradient(45deg, #ff6b35, #f7931e, #ffd700);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 10px;
            text-align: center;
            border: 3px solid white;
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        .toolbar {
            padding: 25px 30px;
            background: #f8f9fa;
            border-bottom: 1px solid #e9ecef;
            display: flex;
            gap: 15px;
            flex-wrap: wrap;
            align-items: center;
        }
        .btn {
            padding: 12px 20px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
            text-decoration: none;
            color: white;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }
        .btn-primary { background: linear-gradient(135deg, #28a745, #20c997); }
        .btn-secondary { background: linear-gradient(135deg, #6c757d, #495057); }
        .btn-danger { background: linear-gradient(135deg, #dc3545, #c82333); }
        .btn-info { background: linear-gradient(135deg, #17a2b8, #138496); }
        .stats {
            padding: 20px 30px;
            background: linear-gradient(135deg, #e3f2fd, #f3e5f5);
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
            gap: 20px;
        }
        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
            text-align: center;
            flex: 1;
            min-width: 200px;
        }
        .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #4472C4;
            margin-bottom: 5px;
        }
        .stat-label {
            color: #6c757d;
            font-size: 14px;
        }
        .groups-grid {
            padding: 30px;
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
        }
        .group-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 12px;
            padding: 25px;
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }
        .group-card:hover {
            border-color: #4472C4;
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .group-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: linear-gradient(135deg, #4472C4, #5B9BD5);
        }
        .group-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .group-name {
            font-size: 20px;
            font-weight: bold;
            color: #2c3e50;
        }
        .group-number {
            background: linear-gradient(135deg, #4472C4, #5B9BD5);
            color: white;
            padding: 5px 12px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: bold;
        }
        .group-info {
            margin-bottom: 20px;
        }
        .group-info div {
            margin-bottom: 8px;
            color: #6c757d;
            font-size: 14px;
        }
        .group-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }
        .btn-small {
            padding: 8px 15px;
            font-size: 12px;
            border-radius: 6px;
        }
        .add-group-card {
            background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
            border: 2px dashed #28a745;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-align: center;
            color: #28a745;
            font-size: 18px;
            font-weight: bold;
            min-height: 200px;
        }
        .add-group-card:hover {
            background: linear-gradient(135deg, #d4edda, #e8f5e8);
            border-color: #20c997;
            color: #20c997;
        }
        .add-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
            backdrop-filter: blur(5px);
        }
        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
            animation: modalSlideIn 0.3s ease;
        }
        @keyframes modalSlideIn {
            from { transform: translateY(-50px); opacity: 0; }
            to { transform: translateY(0); opacity: 1; }
        }
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 25px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }
        .modal-title {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }
        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.3s ease;
        }
        .close:hover {
            color: #dc3545;
        }
        .form-group {
            margin-bottom: 20px;
        }
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        .form-control {
            width: 100%;
            padding: 12px 15px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }
        .form-control:focus {
            outline: none;
            border-color: #4472C4;
            box-shadow: 0 0 0 3px rgba(68, 114, 196, 0.1);
        }
        .form-actions {
            display: flex;
            gap: 15px;
            justify-content: flex-end;
            margin-top: 30px;
        }
        @media (max-width: 768px) {
            .container { margin: 10px; border-radius: 10px; }
            .header { padding: 20px; }
            .header h1 { font-size: 22px; }
            .toolbar { padding: 15px 20px; flex-direction: column; align-items: stretch; }
            .groups-grid { grid-template-columns: 1fr; padding: 20px; }
            .stats { flex-direction: column; }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo-corner">Facilite<br>Chaef</div>
            <h1>GESTIONNAIRE DE GROUPES</h1>
            <p>Système de gestion des groupes de commandes clients</p>
        </div>

        <div class="toolbar">
            <button class="btn btn-primary" onclick="ouvrirModalNouveauGroupe()">➕ Nouveau Groupe</button>
            <button class="btn btn-secondary" onclick="actualiserGroupes()">🔄 Actualiser</button>
            <button class="btn btn-info" onclick="exporterGroupes()">📊 Exporter Données</button>
            <button class="btn btn-danger" onclick="supprimerGroupesSelectionnes()">🗑️ Supprimer Sélectionnés</button>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalGroupes">0</div>
                <div class="stat-label">Groupes Totaux</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="totalClients">0</div>
                <div class="stat-label">Clients Totaux</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="chiffreAffaires">0 DA</div>
                <div class="stat-label">Chiffre d'Affaires</div>
            </div>
        </div>

        <div class="groups-grid" id="groupsGrid"></div>
    </div>

    <div id="modalNouveauGroupe" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Créer Nouveau Groupe</h2>
                <span class="close" onclick="fermerModal()">&times;</span>
            </div>
            <form id="formNouveauGroupe">
                <div class="form-group">
                    <label class="form-label" for="nomGroupe">Nom du Groupe *</label>
                    <input type="text" id="nomGroupe" class="form-control" placeholder="Ex: GROUPE 03" required>
                </div>
                <div class="form-group">
                    <label class="form-label" for="descriptionGroupe">Description</label>
                    <textarea id="descriptionGroupe" class="form-control" rows="3" placeholder="Description du groupe (optionnel)"></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label" for="dateCreation">Date de Création</label>
                    <input type="date" id="dateCreation" class="form-control">
                </div>
                <div class="form-actions">
                    <button type="button" class="btn btn-secondary" onclick="fermerModal()">Annuler</button>
                    <button type="submit" class="btn btn-primary">Créer Groupe</button>
                </div>
            </form>
        </div>
    </div>

    <script>
        let groupes = [];

        document.addEventListener('DOMContentLoaded', function() {
            chargerGroupes();
            mettreAJourStatistiques();
            document.getElementById('dateCreation').value = new Date().toISOString().split('T')[0];
        });

        function chargerGroupes() {
            const groupesSauvegardes = localStorage.getItem('groupes');
            if (groupesSauvegardes) {
                groupes = JSON.parse(groupesSauvegardes);
            } else {
                groupes = [{
                    id: 'groupe-02',
                    nom: 'GROUPE 02',
                    description: 'Groupe principal de commandes clients',
                    dateCreation: '2024-10-21',
                    nombreClients: 0,
                    chiffreAffaires: 0,
                    statut: 'actif'
                }];
                sauvegarderGroupes();
            }
            afficherGroupes();
        }

        function sauvegarderGroupes() {
            localStorage.setItem('groupes', JSON.stringify(groupes));
        }

        function afficherGroupes() {
            const grid = document.getElementById('groupsGrid');
            grid.innerHTML = '';

            const addCard = document.createElement('div');
            addCard.className = 'group-card add-group-card';
            addCard.onclick = ouvrirModalNouveauGroupe;
            addCard.innerHTML = '<div class="add-icon">➕</div><div>Créer Nouveau Groupe</div>';
            grid.appendChild(addCard);

            groupes.forEach(groupe => {
                const card = document.createElement('div');
                card.className = 'group-card';
                card.onclick = () => ouvrirGroupe(groupe.id);

                card.innerHTML =
                    '<div class="group-header">' +
                        '<div class="group-name">' + groupe.nom + '</div>' +
                        '<div class="group-number">' + groupe.id.toUpperCase() + '</div>' +
                    '</div>' +
                    '<div class="group-info">' +
                        '<div><strong>📅 Créé le:</strong> ' + formatDate(groupe.dateCreation) + '</div>' +
                        '<div><strong>👥 Clients:</strong> ' + (groupe.nombreClients || 0) + '</div>' +
                        '<div><strong>💰 CA:</strong> ' + formatMontant(groupe.chiffreAffaires || 0) + ' DA</div>' +
                        '<div><strong>📝 Description:</strong> ' + (groupe.description || 'Aucune description') + '</div>' +
                    '</div>' +
                    '<div class="group-actions" onclick="event.stopPropagation()">' +
                        '<button class="btn btn-primary btn-small" onclick="creerPageGroupe(\'' + groupe.id + '\')">📄 Ouvrir Page</button>' +
                        '<button class="btn btn-info btn-small" onclick="modifierGroupe(\'' + groupe.id + '\')">✏️ Modifier</button>' +
                        '<button class="btn btn-danger btn-small" onclick="supprimerGroupe(\'' + groupe.id + '\')">🗑️ Supprimer</button>' +
                    '</div>';

                grid.appendChild(card);
            });
        }

        function mettreAJourStatistiques() {
            const totalGroupes = groupes.length;
            let totalClients = 0;
            let chiffreAffaires = 0;

            groupes.forEach(groupe => {
                totalClients += groupe.nombreClients || 0;
                chiffreAffaires += groupe.chiffreAffaires || 0;
            });

            document.getElementById('totalGroupes').textContent = totalGroupes;
            document.getElementById('totalClients').textContent = totalClients;
            document.getElementById('chiffreAffaires').textContent = formatMontant(chiffreAffaires) + ' DA';
        }

        function ouvrirModalNouveauGroupe() {
            document.getElementById('modalNouveauGroupe').style.display = 'block';
            document.getElementById('nomGroupe').focus();
        }

        function fermerModal() {
            document.getElementById('modalNouveauGroupe').style.display = 'none';
            document.getElementById('formNouveauGroupe').reset();
            document.getElementById('dateCreation').value = new Date().toISOString().split('T')[0];
        }

        document.getElementById('formNouveauGroupe').addEventListener('submit', function(e) {
            e.preventDefault();

            const nom = document.getElementById('nomGroupe').value.trim();
            const description = document.getElementById('descriptionGroupe').value.trim();
            const dateCreation = document.getElementById('dateCreation').value;

            if (!nom) {
                alert('Le nom du groupe est obligatoire!');
                return;
            }

            if (groupes.some(g => g.nom.toLowerCase() === nom.toLowerCase())) {
                alert('Un groupe avec ce nom existe déjà!');
                return;
            }

            const id = 'groupe-' + nom.toLowerCase().replace(/[^a-z0-9]/g, '-');

            const nouveauGroupe = {
                id: id,
                nom: nom,
                description: description,
                dateCreation: dateCreation,
                nombreClients: 0,
                chiffreAffaires: 0,
                statut: 'actif'
            };

            groupes.push(nouveauGroupe);
            sauvegarderGroupes();
            afficherGroupes();
            mettreAJourStatistiques();
            fermerModal();

            alert('Groupe "' + nom + '" créé avec succès!');
        });

        function ouvrirGroupe(groupeId) {
            creerPageGroupe(groupeId);
        }

        function creerPageGroupe(groupeId) {
            const groupe = groupes.find(g => g.id === groupeId);
            if (groupe) {
                if (groupe.id === 'groupe-02') {
                    window.location.href = 'GestionnaireGROUPE 02.html';
                } else {
                    const nomFichier = 'Gestionnaire_' + groupe.nom.replace(/[^a-zA-Z0-9]/g, '_') + '.html';
                    const contenuHTML = genererHTMLGroupe(groupe);

                    const blob = new Blob([contenuHTML], { type: 'text/html' });
                    const url = URL.createObjectURL(blob);
                    const link = document.createElement('a');
                    link.href = url;
                    link.download = nomFichier;
                    link.click();
                    URL.revokeObjectURL(url);

                    alert('✅ Page créée: ' + nomFichier + '\n\n📄 Sauvegardez ce fichier dans votre dossier\n🔧 Cette page est vide et prête pour vos données!\n\n📋 Fonctionnalités incluses:\n• Ajouter des commandes\n• Gérer les clients\n• Calculer les totaux\n• Exporter vers Excel\n• Générer des factures');
                }
            }
        }

        function modifierGroupe(groupeId) {
            const groupe = groupes.find(g => g.id === groupeId);
            if (groupe) {
                const nouveauNom = prompt('Nouveau nom du groupe:', groupe.nom);
                if (nouveauNom && nouveauNom.trim()) {
                    if (groupes.some(g => g.id !== groupeId && g.nom.toLowerCase() === nouveauNom.toLowerCase())) {
                        alert('Un groupe avec ce nom existe déjà!');
                        return;
                    }

                    groupe.nom = nouveauNom.trim();

                    const nouvelleDescription = prompt('Nouvelle description:', groupe.description || '');
                    if (nouvelleDescription !== null) {
                        groupe.description = nouvelleDescription.trim();
                    }

                    sauvegarderGroupes();
                    afficherGroupes();
                    alert('Groupe modifié avec succès!');
                }
            }
        }

        function supprimerGroupe(groupeId) {
            event.stopPropagation();

            const groupe = groupes.find(g => g.id === groupeId);
            if (groupe) {
                if (groupe.id === 'groupe-02') {
                    if (!confirm('⚠️ ATTENTION: Vous êtes sur le point de supprimer GROUPE 02 qui contient vos données principales!\n\nÊtes-vous absolument sûr de vouloir continuer?\n\nCette action supprimera:\n• Le groupe GROUPE 02\n• Toutes ses données de commandes\n• Tous les clients associés\n\nCette action est IRRÉVERSIBLE!')) {
                        return;
                    }
                }

                const message = 'Êtes-vous sûr de vouloir supprimer le groupe "' + groupe.nom + '"?\n\nCette action supprimera:\n• Le groupe et ses informations\n• Toutes les données de commandes associées\n• Tous les clients de ce groupe\n\nCette action est irréversible!';

                if (confirm(message)) {
                    localStorage.removeItem('commandesData_' + groupeId);
                    groupes = groupes.filter(g => g.id !== groupeId);
                    sauvegarderGroupes();
                    afficherGroupes();
                    mettreAJourStatistiques();
                    alert('✅ Groupe "' + groupe.nom + '" supprimé avec succès!\n\n🗑️ Toutes les données associées ont été effacées.');
                }
            }
        }

        function actualiserGroupes() {
            groupes.forEach(groupe => {
                const commandesData = localStorage.getItem('commandesData_' + groupe.id);
                if (commandesData) {
                    const commandes = JSON.parse(commandesData);
                    groupe.nombreClients = commandes.length;
                    groupe.chiffreAffaires = commandes.reduce((total, cmd) => total + (cmd.prix || 0), 0);
                } else {
                    groupe.nombreClients = 0;
                    groupe.chiffreAffaires = 0;
                }
            });

            sauvegarderGroupes();
            afficherGroupes();
            mettreAJourStatistiques();
            alert('Données actualisées!');
        }

        function exporterGroupes() {
            const dataStr = JSON.stringify(groupes, null, 2);
            const dataBlob = new Blob([dataStr], {type: 'application/json'});

            const link = document.createElement('a');
            link.href = URL.createObjectURL(dataBlob);
            link.download = 'groupes_export_' + new Date().toISOString().split('T')[0] + '.json';
            link.click();
        }

        function supprimerGroupesSelectionnes() {
            alert('Fonctionnalité en développement: Sélection multiple de groupes');
        }

        function formatDate(dateStr) {
            const date = new Date(dateStr);
            return date.toLocaleDateString('fr-FR');
        }

        function formatMontant(montant) {
            return new Intl.NumberFormat('fr-FR').format(montant);
        }

        window.onclick = function(event) {
            const modal = document.getElementById('modalNouveauGroupe');
            if (event.target === modal) {
                fermerModal();
            }
        }

        document.addEventListener('keydown', function(e) {
            if (e.key === 'Escape') {
                fermerModal();
            }
            if (e.ctrlKey && e.key === 'n') {
                e.preventDefault();
                ouvrirModalNouveauGroupe();
            }
        });
    </script>
</body>
</html>