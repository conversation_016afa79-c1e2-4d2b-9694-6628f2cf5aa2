@echo off
echo ========================================
echo Compilation du Gestionnaire de Commandes
echo ========================================
echo.

echo Vérification de .NET...
dotnet --version
if %errorlevel% neq 0 (
    echo ERREUR: .NET n'est pas installé!
    echo Veuillez exécuter installer_dotnet.bat d'abord.
    pause
    exit /b 1
)

echo.
echo Restauration des packages...
dotnet restore

echo.
echo Compilation de l'application...
dotnet build --configuration Release

echo.
echo Création de l'exécutable...
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true

echo.
echo ========================================
echo Compilation terminée!
echo ========================================
echo.
echo L'exécutable se trouve dans:
echo bin\Release\net6.0-windows\win-x64\publish\CustomerOrderManager.exe
echo.

pause
