# Gestionnaire de Commandes Clients - GROUPE 02

## Description
Application Windows Forms en C# pour la gestion des commandes clients avec interface classique en français.

## Fonctionnalités
- ✅ Affichage des commandes dans un tableau
- ✅ Ajout de nouvelles commandes
- ✅ Suppression de commandes
- ✅ Colonnes de suivi (1-10) pour le statut avec calcul automatique du Total Payé
- ✅ Nombre de colonnes configurable (1-20) avec recalcul automatique
- ✅ Base de données SQLite intégrée
- ✅ Interface utilisateur classique Windows

## Structure des données
Chaque commande contient:
- **N°**: Numéro automatique
- **REF CLI**: Référence client
- **NOM ET PRÉNOM**: Nom complet du client
- **ARTICLES COM**: Description des articles commandés
- **DATE DE COM**: Date de la commande
- **PRIX**: Prix total
- **TOTAL PAYÉ**: Calculé automatiquement selon la formule: `(Nombre de statuts cochés × Prix) ÷ Nombre de colonnes`
- **RESTANT**: Montant restant à payer (Prix - Total Payé)
- **Colonnes 1-10**: Statuts de suivi
- **Statistiques globales**: Total général, total payé, et montant restant pour tous les clients

## Installation et Compilation

### Prérequis
- .NET 6.0 ou supérieur
- Windows 10/11

### Compilation
```bash
# Cloner ou télécharger le projet
cd CustomerOrderManager

# Restaurer les packages NuGet
dotnet restore

# Compiler l'application
dotnet build --configuration Release

# Créer un exécutable
dotnet publish -c Release -r win-x64 --self-contained true -p:PublishSingleFile=true
```

### Exécution
```bash
# Exécuter en mode développement
dotnet run

# Ou exécuter le fichier .exe généré
./bin/Release/net6.0-windows/win-x64/publish/CustomerOrderManager.exe
```

## Utilisation

### Ajouter une commande
1. Cliquer sur le bouton "Ajouter Commande"
2. Remplir tous les champs obligatoires
3. Cliquer sur "Sauvegarder"

### Supprimer une commande
1. Sélectionner une ligne dans le tableau
2. Cliquer sur "Supprimer"
3. Confirmer la suppression

### Actualiser les données
- Cliquer sur "Actualiser" pour recharger les données

## Base de données
- **Type**: SQLite
- **Fichier**: `orders.db` (créé automatiquement)
- **Emplacement**: Dossier de l'application

## Personnalisation
Le code est structuré pour faciliter les modifications:
- `DatabaseManager.cs`: Gestion de la base de données
- `MainForm.cs`: Interface principale
- `AjouterCommandeForm.cs`: Formulaire d'ajout

## Support
Pour toute question ou problème, veuillez contacter l'équipe de développement.

---
**GROUPE 02** - Gestionnaire de Commandes Clients
