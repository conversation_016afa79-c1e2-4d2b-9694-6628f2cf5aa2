<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestionnaire Premium - GROUPE 02 (Database)</title>
    <!-- Bibliothèque SheetJS pour Excel -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <!-- Styles avancés -->
    <link rel="stylesheet" href="styles-advanced.css">
    <!-- Icônes Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <style>
        /* Styles spécifiques à cette page */
        .control-columns {
            display: flex;
            align-items: center;
            gap: 4px;
            font-size: 11px;
            margin-top: 6px;
            background: rgba(102, 126, 234, 0.1);
            padding: 6px 10px;
            border-radius: 10px;
            transition: all 0.3s ease;
        }

        .control-columns:hover {
            background: rgba(102, 126, 234, 0.15);
            transform: scale(1.02);
        }

        .control-columns input {
            width: 55px !important;
            padding: 6px 8px !important;
            font-size: 11px !important;
            text-align: center;
            border: 2px solid rgba(102, 126, 234, 0.3) !important;
            border-radius: 8px !important;
            font-weight: 700 !important;
            background: white !important;
            transition: all 0.3s ease !important;
        }

        .control-columns input:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2) !important;
            transform: scale(1.05) !important;
        }

        .control-columns span {
            color: #667eea;
            font-size: 10px;
            font-weight: 700;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .number-cell {
            text-align: center;
            vertical-align: middle;
            padding: 12px 8px;
        }

        .number-display {
            font-weight: 800;
            font-size: 18px;
            color: #667eea;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.1);
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .floating-action {
            position: fixed;
            bottom: 30px;
            right: 30px;
            width: 60px;
            height: 60px;
            background: var(--primary-gradient);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            cursor: pointer;
            box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            z-index: 100;
        }

        .floating-action:hover {
            transform: scale(1.1) rotate(90deg);
            box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
        }

        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 15px 20px;
            border-radius: 12px;
            color: white;
            font-weight: 600;
            z-index: 1001;
            transform: translateX(400px);
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        }

        .notification.show {
            transform: translateX(0);
        }

        .notification.success {
            background: var(--success-gradient);
            box-shadow: 0 6px 20px rgba(81, 207, 102, 0.4);
        }

        .notification.error {
            background: var(--danger-gradient);
            box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4);
        }

        .notification.info {
            background: var(--info-gradient);
            box-shadow: 0 6px 20px rgba(116, 185, 255, 0.4);
        }

        .toolbar-section {
            display: flex;
            align-items: center;
            gap: 15px;
            padding: 12px 20px;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            backdrop-filter: blur(10px);
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
        }

        .toolbar-label {
            font-weight: 700;
            color: var(--text-primary);
            font-size: 14px;
            white-space: nowrap;
        }

        .toolbar-input {
            width: 80px !important;
            padding: 10px 15px !important;
            border: 2px solid rgba(102, 126, 234, 0.2) !important;
            border-radius: 10px !important;
            text-align: center !important;
            font-weight: 700 !important;
            background: white !important;
            transition: all 0.3s ease !important;
        }

        .toolbar-input:focus {
            border-color: #667eea !important;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
            transform: scale(1.05) !important;
        }

        .stats-container {
            margin-left: auto;
            display: flex;
            gap: 20px;
            align-items: center;
        }

        /* Animation pour les changements de données */
        .data-updated {
            animation: dataUpdate 0.6s ease;
        }

        @keyframes dataUpdate {
            0% { background: rgba(102, 126, 234, 0.3); transform: scale(1.02); }
            100% { background: transparent; transform: scale(1); }
        }

        /* Indicateur de connexion */
        .connection-status {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 12px;
            font-weight: 600;
            color: #27ae60;
        }

        .connection-dot {
            width: 8px;
            height: 8px;
            background: #27ae60;
            border-radius: 50%;
            animation: connectionPulse 2s infinite;
        }

        @keyframes connectionPulse {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.6; transform: scale(1.2); }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 id="titreGroupe">
                <i class="fas fa-database"></i>
                GESTIONNAIRE PREMIUM - GROUPE 02
            </h1>
            <div class="database-indicator">
                <i class="fas fa-server"></i>
                Connecté à la base de données
            </div>
            <div class="connection-status">
                <div class="connection-dot"></div>
                <span>Connexion active</span>
            </div>
            <div style="margin-top: 20px;">
                <button class="btn btn-back" onclick="retournerGroupes()">
                    <i class="fas fa-arrow-left"></i>
                    Retour aux Groupes
                </button>
            </div>
        </div>
        
        <div class="toolbar">
            <button class="btn btn-primary" onclick="ouvrirModalAjout()">
                <i class="fas fa-plus"></i>
                Ajouter Commande
            </button>
            <button class="btn btn-danger" onclick="supprimerCommande()">
                <i class="fas fa-trash"></i>
                Supprimer
            </button>
            <button class="btn btn-success" onclick="actualiserDonnees()">
                <i class="fas fa-sync-alt"></i>
                Actualiser
            </button>
            <button class="btn btn-excel" onclick="exporterExcel()">
                <i class="fas fa-file-excel"></i>
                Exporter Excel
            </button>

            <!-- Contrôle du nombre de colonnes par défaut -->
            <div class="toolbar-section">
                <label for="nombreColonnes" class="toolbar-label">
                    <i class="fas fa-columns"></i>
                    Colonnes par défaut:
                </label>
                <input type="number" id="nombreColonnes" value="10" min="1" max="20" class="toolbar-input">
            </div>

            <!-- Statistiques -->
            <div class="stats-container">
                <div class="stat-card">
                    <div class="stat-label">
                        <i class="fas fa-calculator"></i>
                        Total Général
                    </div>
                    <div class="stat-value" id="totalGeneral">0,00 DA</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">
                        <i class="fas fa-money-bill-wave"></i>
                        Total Payé
                    </div>
                    <div class="stat-value" id="totalPaye">0,00 DA</div>
                </div>
                <div class="stat-card">
                    <div class="stat-label">
                        <i class="fas fa-exclamation-triangle"></i>
                        Restant
                    </div>
                    <div class="stat-value" id="totalRestant">0,00 DA</div>
                </div>
            </div>
        </div>

        <div id="messageContainer"></div>

        <div class="table-container">
            <div id="loadingIndicator" class="loading">
                <i class="fas fa-database"></i>
                Chargement des données depuis la base de données...
            </div>
            <table id="tableCommandes" style="display: none;">
                <thead>
                    <tr id="headerRow">
                        <th class="num-col">
                            <i class="fas fa-hashtag"></i>
                            N°
                        </th>
                        <th class="ref-col">
                            <i class="fas fa-barcode"></i>
                            REF CLI
                        </th>
                        <th class="nom-col">
                            <i class="fas fa-user"></i>
                            NOM ET PRÉNOM
                        </th>
                        <th class="articles-col">
                            <i class="fas fa-box"></i>
                            ARTICLES COM
                        </th>
                        <th class="date-col">
                            <i class="fas fa-calendar"></i>
                            DATE DE COM
                        </th>
                        <th class="prix-col">
                            <i class="fas fa-tag"></i>
                            PRIX
                        </th>
                        <th class="total-col">
                            <i class="fas fa-coins"></i>
                            TOTAL PAYÉ
                        </th>
                        <th class="total-col">
                            <i class="fas fa-clock"></i>
                            RESTANT
                        </th>
                        <!-- Les colonnes de statut seront ajoutées dynamiquement -->
                    </tr>
                </thead>
                <tbody id="corpsTable">
                </tbody>
            </table>
        </div>
    </div>

    <!-- Bouton d'action flottant -->
    <div class="floating-action" onclick="ouvrirModalAjout()" title="Ajouter une nouvelle commande">
        <i class="fas fa-plus"></i>
    </div>

    <!-- Modal d'ajout amélioré -->
    <div id="modalAjout" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="close" onclick="fermerModal()">&times;</span>
                <h3>
                    <i class="fas fa-plus-circle"></i>
                    Ajouter une Nouvelle Commande
                </h3>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="refClient">
                        <i class="fas fa-barcode"></i>
                        Référence Client:
                    </label>
                    <input type="text" id="refClient" placeholder="Ex: OS24S1793">
                </div>
                <div class="form-group">
                    <label for="nomPrenom">
                        <i class="fas fa-user"></i>
                        Nom et Prénom:
                    </label>
                    <input type="text" id="nomPrenom" placeholder="Ex: BEN SEGHIR ABDERAHMANE">
                </div>
                <div class="form-group">
                    <label for="articles">
                        <i class="fas fa-box"></i>
                        Articles:
                    </label>
                    <textarea id="articles" placeholder="Description des articles commandés"></textarea>
                </div>
                <div class="form-group">
                    <label for="dateCommande">
                        <i class="fas fa-calendar"></i>
                        Date de Commande:
                    </label>
                    <input type="date" id="dateCommande">
                </div>
                <div class="form-group">
                    <label for="prix">
                        <i class="fas fa-tag"></i>
                        Prix:
                    </label>
                    <input type="number" id="prix" step="0.01" placeholder="0.00">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-success" onclick="sauvegarderCommande()">
                    <i class="fas fa-save"></i>
                    Sauvegarder
                </button>
                <button class="btn" onclick="fermerModal()">
                    <i class="fas fa-times"></i>
                    Annuler
                </button>
            </div>
        </div>
    </div>

    <script>
        // Configuration de l'API
        const API_BASE_URL = 'http://localhost:8080/api';
        
        let commandes = [];
        let ligneSelectionnee = null;
        let nombreColonnesDefaut = 10;

        // Fonctions utilitaires pour l'API
        async function apiRequest(endpoint, options = {}) {
            try {
                const response = await fetch(`${API_BASE_URL}${endpoint}`, {
                    headers: {
                        'Content-Type': 'application/json',
                        ...options.headers
                    },
                    ...options
                });

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                return await response.json();
            } catch (error) {
                console.error('خطأ في API:', error);
                afficherNotification(`خطأ في الاتصال: ${error.message}`, 'error');
                throw error;
            }
        }

        function afficherNotification(message, type = 'info') {
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            
            const icons = {
                'success': 'fas fa-check-circle',
                'error': 'fas fa-exclamation-circle',
                'info': 'fas fa-info-circle'
            };
            
            notification.innerHTML = `
                <i class="${icons[type] || 'fas fa-info-circle'}"></i>
                ${message}
            `;
            
            document.body.appendChild(notification);
            
            setTimeout(() => notification.classList.add('show'), 100);
            
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => document.body.removeChild(notification), 400);
            }, 4000);
        }

        async function chargerDonnees() {
            try {
                document.getElementById('loadingIndicator').style.display = 'flex';
                document.getElementById('tableCommandes').style.display = 'none';

                const response = await apiRequest('/commandes');
                commandes = response.data;

                creerColonnesStatut();
                afficherCommandes();
                mettreAJourStatistiques();

                document.getElementById('loadingIndicator').style.display = 'none';
                document.getElementById('tableCommandes').style.display = 'table';

                console.log(`✅ تم تحميل ${commandes.length} طلب من قاعدة البيانات`);
            } catch (error) {
                document.getElementById('loadingIndicator').innerHTML =
                    `<i class="fas fa-exclamation-triangle"></i>
                     فشل في تحميل البيانات: ${error.message}
                     <br><br>
                     <button onclick="chargerDonnees()" class="btn btn-primary">
                         <i class="fas fa-redo"></i> إعادة المحاولة
                     </button>`;
            }
        }

        function creerColonnesStatut() {
            const headerRow = document.getElementById('headerRow');
            const existingStatusHeaders = headerRow.querySelectorAll('.status-header');
            existingStatusHeaders.forEach(header => header.remove());

            const maxColonnes = Math.max(...commandes.map(cmd => cmd.nombreColonnes || 10), nombreColonnesDefaut);

            for (let i = 1; i <= maxColonnes; i++) {
                const th = document.createElement('th');
                th.className = 'status-col status-header';
                th.innerHTML = `<i class="fas fa-check"></i> ${i}`;
                headerRow.appendChild(th);
            }
        }

        function afficherCommandes() {
            const tbody = document.getElementById('corpsTable');
            tbody.innerHTML = '';

            const maxColonnes = Math.max(...commandes.map(cmd => cmd.nombreColonnes || 10), nombreColonnesDefaut);

            commandes.forEach(commande => {
                const tr = document.createElement('tr');
                tr.onclick = () => selectionnerLigne(tr, commande.id);

                if (!commande.nombreColonnes) {
                    commande.nombreColonnes = 10;
                }

                let statutsHtml = '';
                for (let i = 0; i < maxColonnes; i++) {
                    if (i < commande.nombreColonnes) {
                        const isChecked = i < commande.statuts.length ? commande.statuts[i] : 0;
                        statutsHtml += `<td><input type="checkbox" class="status-checkbox" ${isChecked ? 'checked' : ''} onchange="changerStatut(${commande.id}, ${i}, this.checked)"></td>`;
                    } else {
                        statutsHtml += `<td style="background: linear-gradient(135deg, rgba(0,0,0,0.02) 0%, rgba(0,0,0,0.05) 100%);"></td>`;
                    }
                }

                const restant = commande.prix - commande.totalPaye;

                const controlColonnes = `
                    <div class="control-columns">
                        <input type="number" value="${commande.nombreColonnes}" min="1" max="20"
                               onchange="changerNombreColonnesCommande(${commande.id}, this.value)"
                               title="عدد الأعمدة لهذا الطلب">
                        <span>cols</span>
                    </div>
                `;

                tr.innerHTML = `
                    <td class="number-cell">
                        <div class="number-display">${commande.id}</div>
                        ${controlColonnes}
                    </td>
                    <td style="font-weight: 600;">${commande.refClient}</td>
                    <td style="font-weight: 600; text-align: left; padding-left: 15px;">${commande.nomPrenom}</td>
                    <td style="text-align: left; padding-left: 15px; color: #7f8c8d;">${commande.articles}</td>
                    <td style="font-weight: 600;">${commande.dateCommande}</td>
                    <td style="font-weight: 700; color: #667eea;">${commande.prix.toLocaleString('fr-FR', {minimumFractionDigits: 2})} DA</td>
                    <td style="color: ${commande.totalPaye > 0 ? '#51cf66' : '#95a5a6'}; font-weight: 700;">${commande.totalPaye.toLocaleString('fr-FR', {minimumFractionDigits: 2})} DA</td>
                    <td style="color: ${restant > 0 ? '#ff6b6b' : '#51cf66'}; font-weight: 700;">${restant.toLocaleString('fr-FR', {minimumFractionDigits: 2})} DA</td>
                    ${statutsHtml}
                `;

                tbody.appendChild(tr);
            });
        }

        function mettreAJourStatistiques() {
            const totalGeneral = commandes.reduce((sum, cmd) => sum + cmd.prix, 0);
            const totalPaye = commandes.reduce((sum, cmd) => sum + cmd.totalPaye, 0);
            const totalRestant = totalGeneral - totalPaye;

            document.getElementById('totalGeneral').textContent = `${totalGeneral.toLocaleString('fr-FR', {minimumFractionDigits: 2})} DA`;
            document.getElementById('totalPaye').textContent = `${totalPaye.toLocaleString('fr-FR', {minimumFractionDigits: 2})} DA`;
            document.getElementById('totalRestant').textContent = `${totalRestant.toLocaleString('fr-FR', {minimumFractionDigits: 2})} DA`;

            const elementGeneral = document.getElementById('totalGeneral');
            const elementPaye = document.getElementById('totalPaye');
            const elementRestant = document.getElementById('totalRestant');

            elementGeneral.style.color = '#667eea';
            elementPaye.style.color = totalPaye > 0 ? '#51cf66' : '#95a5a6';

            if (totalRestant <= 0) {
                elementRestant.style.color = '#51cf66';
                elementRestant.parentElement.style.background = 'linear-gradient(135deg, rgba(81, 207, 102, 0.1) 0%, rgba(64, 192, 87, 0.1) 100%)';
            } else {
                elementRestant.style.color = '#ff6b6b';
                elementRestant.parentElement.style.background = 'linear-gradient(135deg, rgba(255, 107, 107, 0.1) 0%, rgba(238, 90, 82, 0.1) 100%)';
            }

            [elementGeneral, elementPaye, elementRestant].forEach(el => {
                el.classList.add('data-updated');
                setTimeout(() => el.classList.remove('data-updated'), 600);
            });
        }

        function selectionnerLigne(tr, id) {
            document.querySelectorAll('tr').forEach(row => row.classList.remove('selected'));
            tr.classList.add('selected');
            ligneSelectionnee = id;
        }

        async function changerStatut(id, index, estCoche) {
            try {
                await apiRequest('/statuts', {
                    method: 'PUT',
                    body: JSON.stringify({
                        id: id,
                        index: index,
                        value: estCoche
                    })
                });

                await chargerDonnees();
                afficherNotification('✅ تم تحديث الحالة بنجاح', 'success');
            } catch (error) {
                const checkbox = event.target;
                checkbox.checked = !estCoche;
                afficherNotification('❌ فشل في تحديث الحالة', 'error');
            }
        }

        async function changerNombreColonnesCommande(id, nouveauNombre) {
            const nombre = parseInt(nouveauNombre);

            if (isNaN(nombre) || nombre < 1 || nombre > 20) {
                afficherNotification('⚠️ يرجى إدخال رقم بين 1 و 20', 'error');
                await chargerDonnees();
                return;
            }

            try {
                await apiRequest('/commandes', {
                    method: 'PUT',
                    body: JSON.stringify({
                        id: id,
                        nombreColonnes: nombre
                    })
                });

                await chargerDonnees();
                afficherNotification(`✅ تم تغيير عدد الأعمدة للطلب ${id} إلى ${nombre}`, 'success');
            } catch (error) {
                afficherNotification('❌ فشل في تحديث عدد الأعمدة', 'error');
                await chargerDonnees();
            }
        }

        function ouvrirModalAjout() {
            document.getElementById('modalAjout').style.display = 'block';
            document.getElementById('dateCommande').value = new Date().toISOString().split('T')[0];
            document.body.style.overflow = 'hidden';
        }

        function fermerModal() {
            document.getElementById('modalAjout').style.display = 'none';
            document.body.style.overflow = 'auto';
            ['refClient', 'nomPrenom', 'articles', 'prix'].forEach(id => {
                document.getElementById(id).value = '';
            });
        }

        async function sauvegarderCommande() {
            const refClient = document.getElementById('refClient').value.trim();
            const nomPrenom = document.getElementById('nomPrenom').value.trim();
            const articles = document.getElementById('articles').value.trim();
            const dateCommande = document.getElementById('dateCommande').value;
            const prix = parseFloat(document.getElementById('prix').value) || 0;

            if (!refClient || !nomPrenom || !articles || prix <= 0) {
                afficherNotification('⚠️ يرجى ملء جميع الحقول المطلوبة وإدخال سعر صحيح', 'error');
                return;
            }

            try {
                await apiRequest('/commandes', {
                    method: 'POST',
                    body: JSON.stringify({
                        refClient,
                        nomPrenom,
                        articles,
                        dateCommande: new Date(dateCommande).toLocaleDateString('fr-FR'),
                        prix,
                        totalPaye: 0
                    })
                });

                await chargerDonnees();
                fermerModal();
                afficherNotification('🎉 تم إضافة الطلب بنجاح!', 'success');
            } catch (error) {
                afficherNotification('❌ فشل في إضافة الطلب', 'error');
            }
        }

        async function supprimerCommande() {
            if (!ligneSelectionnee) {
                afficherNotification('⚠️ يرجى اختيار طلب للحذف', 'error');
                return;
            }

            if (confirm('🗑️ هل أنت متأكد من حذف هذا الطلب؟\nلا يمكن التراجع عن هذا الإجراء.')) {
                try {
                    await apiRequest(`/commandes?id=${ligneSelectionnee}`, {
                        method: 'DELETE'
                    });

                    await chargerDonnees();
                    ligneSelectionnee = null;
                    afficherNotification('🗑️ تم حذف الطلب بنجاح!', 'success');
                } catch (error) {
                    afficherNotification('❌ فشل في حذف الطلب', 'error');
                }
            }
        }

        async function actualiserDonnees() {
            await chargerDonnees();
            afficherNotification('🔄 تم تحديث البيانات!', 'success');
        }

        function exporterExcel() {
            afficherNotification('📊 ميزة التصدير ستكون متاحة قريباً', 'info');
        }

        function retournerGroupes() {
            window.location.href = 'GestionnaireGroupes.html';
        }

        // Fermer le modal en cliquant à l'extérieur
        window.onclick = function(event) {
            const modal = document.getElementById('modalAjout');
            if (event.target === modal) {
                fermerModal();
            }
        }

        // Raccourcis clavier
        document.addEventListener('keydown', function(event) {
            if (event.ctrlKey && event.key === 'n') {
                event.preventDefault();
                ouvrirModalAjout();
            }
            if (event.key === 'Escape') {
                fermerModal();
            }
            if (event.key === 'F5') {
                event.preventDefault();
                actualiserDonnees();
            }
        });

        // تحميل البيانات عند بدء التشغيل
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('nombreColonnes').value = nombreColonnesDefaut;
            chargerDonnees();

            // رسالة ترحيب
            setTimeout(() => {
                afficherNotification('🎉 مرحباً بك في النظام المتقدم لإدارة الطلبات!', 'success');
            }, 1000);
        });
    </script>
</body>
</html>
