<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestionnaire de Commandes Clients - GROUPE 02</title>
    <!-- Bibliothèque SheetJS pour Excel -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f0f0f0;
            color: #333;
        }

        .container {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        .header {
            background: linear-gradient(135deg, #4682b4, #5a9fd4);
            color: white;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        }

        .header h1 {
            font-size: 18px;
            font-weight: bold;
            margin: 0;
        }

        .toolbar {
            background-color: #e6e6e6;
            padding: 10px;
            border-bottom: 1px solid #ccc;
            display: flex;
            gap: 10px;
        }

        .btn {
            padding: 8px 16px;
            border: 1px solid #999;
            background: linear-gradient(to bottom, #f8f8f8, #e0e0e0);
            color: #333;
            cursor: pointer;
            font-size: 12px;
            border-radius: 3px;
            transition: all 0.2s;
        }

        .btn:hover {
            background: linear-gradient(to bottom, #e8e8e8, #d0d0d0);
            border-color: #666;
        }

        .btn:active {
            background: linear-gradient(to bottom, #d0d0d0, #e8e8e8);
            box-shadow: inset 1px 1px 3px rgba(0,0,0,0.2);
        }

        .btn-primary { background: linear-gradient(to bottom, #4682b4, #2e5984); color: white; border-color: #2e5984; }
        .btn-danger { background: linear-gradient(to bottom, #dc143c, #b91c3c); color: white; border-color: #b91c3c; }
        .btn-success { background: linear-gradient(to bottom, #228b22, #1e7e1e); color: white; border-color: #1e7e1e; }

        .table-container {
            flex: 1;
            overflow: auto;
            background: white;
            border: 1px solid #ccc;
            margin: 10px;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            font-size: 12px;
        }

        th {
            background: linear-gradient(to bottom, #4682b4, #2e5984);
            color: white;
            padding: 8px 4px;
            text-align: center;
            border: 1px solid #2e5984;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
        }

        td {
            padding: 6px 4px;
            border: 1px solid #ddd;
            text-align: center;
        }

        tr:nth-child(even) {
            background-color: #f8f8f8;
        }

        tr:hover {
            background-color: #add8e6;
        }

        tr.selected {
            background-color: #87ceeb !important;
        }

        .status-checkbox {
            width: 16px;
            height: 16px;
        }

        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: #f0f0f0;
            margin: 5% auto;
            padding: 0;
            border: 2px solid #999;
            width: 500px;
            border-radius: 5px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }

        .modal-header {
            background: linear-gradient(to bottom, #4682b4, #2e5984);
            color: white;
            padding: 10px 15px;
            border-bottom: 1px solid #2e5984;
        }

        .modal-body {
            padding: 20px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
            color: #333;
        }

        .form-group input, .form-group textarea {
            width: 100%;
            padding: 6px;
            border: 1px solid #999;
            border-radius: 3px;
            font-size: 12px;
        }

        .form-group textarea {
            height: 60px;
            resize: vertical;
        }

        .modal-footer {
            padding: 15px;
            text-align: right;
            border-top: 1px solid #ddd;
        }

        .close {
            color: white;
            float: right;
            font-size: 20px;
            font-weight: bold;
            cursor: pointer;
            line-height: 1;
        }

        .close:hover {
            opacity: 0.7;
        }

        .status-col {
            width: 30px;
        }

        .ref-col { width: 100px; }
        .nom-col { width: 200px; }
        .articles-col { width: 250px; }
        .date-col { width: 100px; }
        .prix-col { width: 100px; }
        .total-col { width: 100px; }
    </style>
</head>
<body>
    <div class="container">
        <input type="file" id="logoFactureUpload" accept="image/*" style="display: none;" onchange="traiterLogoFacture(this)">
        <div class="header">
            <h1 id="titreGroupe">GESTIONNAIRE DE COMMANDES CLIENTS - GROUPE 02</h1>
            <div style="margin-top: 10px;">
                <button class="btn" onclick="retournerGroupes()" style="background: linear-gradient(to bottom, #6c757d, #5a6268); color: white; border-color: #6c757d;">
                    ← Retour aux Groupes
                </button>
            </div>
        </div>
        
        <div class="toolbar">
            <button class="btn btn-primary" onclick="ouvrirModalAjout()">Ajouter Commande</button>
            <button class="btn btn-danger" onclick="supprimerCommande()">Supprimer</button>
            <button class="btn btn-success" onclick="actualiserDonnees()">Actualiser</button>
            <button class="btn" onclick="reinitialiserDonnees()" style="background: linear-gradient(to bottom, #ff6b6b, #ee5a5a); color: white; border-color: #ee5a5a;">Réinitialiser</button>
            <button class="btn" onclick="exporterExcel()" style="background: linear-gradient(to bottom, #228b22, #32cd32); color: white; border-color: #228b22;">📊 Exporter Excel</button>
            <button class="btn" onclick="ouvrirFacture()" style="background: linear-gradient(to bottom, #4472C4, #5B9BD5); color: white; border-color: #4472C4;">🧾 Générer Facture</button>
            <button class="btn" onclick="changerLogoFacture()" style="background: linear-gradient(to bottom, #ff6b35, #f7931e); color: white; border-color: #ff6b35;">🖼️ Logo Facture</button>

            <!-- Contrôle du nombre de colonnes -->
            <div style="display: flex; align-items: center; gap: 10px; margin-left: 20px;">
                <label for="nombreColonnes" style="font-weight: bold; color: #2e5984;">Nombre de colonnes:</label>
                <input type="number" id="nombreColonnes" value="10" min="1" max="20" style="width: 60px; padding: 4px; border: 1px solid #999; border-radius: 3px; text-align: center;">
                <button class="btn btn-primary" onclick="appliquerNombreColonnes()" style="padding: 6px 12px;">Appliquer</button>
            </div>

            <!-- Statistiques -->
            <div style="margin-left: auto; display: flex; gap: 20px; align-items: center; font-weight: bold; color: #2e5984;">
                <span id="totalGeneral">TOTAL GÉNÉRAL: 0.00 DA</span>
                <span id="totalPaye">TOTAL PAYÉ: 0.00 DA</span>
                <span id="totalRestant">RESTANT: 0.00 DA</span>
            </div>
        </div>

        <div class="table-container">
            <table id="tableCommandes">
                <thead>
                    <tr id="headerRow">
                        <th>N°</th>
                        <th class="ref-col">REF CLI</th>
                        <th class="nom-col">NOM ET PRÉNOM</th>
                        <th class="articles-col">ARTICLES COM</th>
                        <th class="date-col">DATE DE COM</th>
                        <th class="prix-col">PRIX</th>
                        <th class="total-col">TOTAL PAYÉ</th>
                        <th class="total-col">RESTANT</th>
                        <!-- Les colonnes de statut seront ajoutées dynamiquement -->
                    </tr>
                </thead>
                <tbody id="corpsTable">
                </tbody>
            </table>
        </div>
    </div>

    <!-- Modal d'ajout -->
    <div id="modalAjout" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <span class="close" onclick="fermerModal()">&times;</span>
                <h3>Ajouter une Nouvelle Commande</h3>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label for="refClient">Référence Client:</label>
                    <input type="text" id="refClient" placeholder="Ex: OS24S1793">
                </div>
                <div class="form-group">
                    <label for="nomPrenom">Nom et Prénom:</label>
                    <input type="text" id="nomPrenom" placeholder="Ex: BEN SEGHIR ABDERAHMANE">
                </div>
                <div class="form-group">
                    <label for="articles">Articles:</label>
                    <textarea id="articles" placeholder="Description des articles commandés"></textarea>
                </div>
                <div class="form-group">
                    <label for="dateCommande">Date de Commande:</label>
                    <input type="date" id="dateCommande">
                </div>
                <div class="form-group">
                    <label for="prix">Prix:</label>
                    <input type="number" id="prix" step="0.01" placeholder="0.00">
                </div>
                <div class="form-group">
                    <label for="totalPaye">Total Payé: (Calculé automatiquement)</label>
                    <input type="number" id="totalPaye" step="0.01" placeholder="0.00" readonly style="background-color: #f5f5f5;">
                </div>
            </div>
            <div class="modal-footer">
                <button class="btn btn-success" onclick="sauvegarderCommande()">Sauvegarder</button>
                <button class="btn" onclick="fermerModal()">Annuler</button>
            </div>
        </div>
    </div>

    <script>
        // Charger les données depuis localStorage ou utiliser les données par défaut
        function chargerDonneesInitiales() {
            const groupeActuel = localStorage.getItem('groupeActuel') || 'groupe-02';
            const cleStockage = `commandesData_${groupeActuel}`;
            const donneesStockees = localStorage.getItem(cleStockage);
            if (donneesStockees) {
                return JSON.parse(donneesStockees);
            }

            // Données d'exemple basées sur votre tableau avec statuts calculés et nombre de colonnes individuel
            return [
                {id: 1, refClient: 'OS24S1793', nomPrenom: 'BEN SEGHIR ABDERAHMANE', articles: 'TELE REALME C65 8/256', dateCommande: '24/09/2024', prix: 48000.00, totalPaye: 28800.00, statuts: [1,1,1,1,1,1,0,0,0,0], nombreColonnes: 10},
                {id: 2, refClient: 'OS24S1796', nomPrenom: 'SIGA MOHAMMED', articles: 'REF 580L+ TV 40" VDDA+ TENDEUSE', dateCommande: '29/09/2024', prix: 142900.00, totalPaye: 85740.00, statuts: [1,1,1,1,1,1,0,0,0,0], nombreColonnes: 10},
                {id: 3, refClient: 'OS24S1805', nomPrenom: 'CHABIRA IBRAHIM', articles: 'MEUBLES', dateCommande: '05/10/2024', prix: 22800.00, totalPaye: 13680.00, statuts: [1,1,1,1,1,1,0,0,0,0], nombreColonnes: 10},
                {id: 4, refClient: 'OS24S1807', nomPrenom: 'BEKKAYE ABDELBAKI', articles: 'RADIATEUR A GAZ FG11G', dateCommande: '06/10/2024', prix: 33000.00, totalPaye: 19800.00, statuts: [1,1,1,1,1,1,0,0,0,0], nombreColonnes: 10},
                {id: 5, refClient: 'OS24S1814', nomPrenom: 'MAZHOUD KHAIRA', articles: 'M.LAVER 10.5KG RAYLAN + TV 43" WEBOS', dateCommande: '07/10/2024', prix: 158000.00, totalPaye: 94800.00, statuts: [1,1,1,1,1,1,0,0,0,0], nombreColonnes: 10},
                {id: 6, refClient: 'OS24S1810', nomPrenom: 'KOURRINI ATTIA', articles: 'CUISINIER 4 FEUX ENEIM', dateCommande: '07/10/2024', prix: 69000.00, totalPaye: 20700.00, statuts: [1,1,1,0,0,0,0,0,0,0], nombreColonnes: 10},
                {id: 7, refClient: 'OS24S1811', nomPrenom: 'DJAMED ELHOUCINE', articles: 'TV 43" GOOGLE TV STREAM', dateCommande: '07/10/2024', prix: 61000.00, totalPaye: 36600.00, statuts: [1,1,1,1,1,1,0,0,0,0], nombreColonnes: 10},
                {id: 8, refClient: 'OS24S1822', nomPrenom: 'DJIREB AMINA', articles: 'CHAUF-EAU 6L+ RADIATEUR GAZ', dateCommande: '09/10/2024', prix: 49750.00, totalPaye: 29850.00, statuts: [1,1,1,1,1,1,0,0,0,0], nombreColonnes: 10},
                {id: 9, refClient: 'OS24S1830', nomPrenom: 'HAMDANI HICHAM', articles: 'RADIATEUR GAZ 14KW + TENDEUSE', dateCommande: '12/10/2024', prix: 55300.00, totalPaye: 33180.00, statuts: [1,1,1,1,1,1,0,0,0,0], nombreColonnes: 10},
                {id: 10, refClient: 'OS24S1832', nomPrenom: 'DZIRI MOHAMED', articles: 'MICRO ONDES 23 L', dateCommande: '14/10/2024', prix: 24000.00, totalPaye: 2400.00, statuts: [1,0,0,0,0,0,0,0,0,0], nombreColonnes: 10}
            ];
        }

        let groupeActuel = localStorage.getItem('groupeActuel') || 'groupe-02';
        let commandes = chargerDonneesInitiales();

        let ligneSelectionnee = null;
        let prochainId = 11;
        let nombreColonnesActuel = parseInt(localStorage.getItem('nombreColonnes')) || 10;

        function creerColonnesStatut() {
            const headerRow = document.getElementById('headerRow');

            // Supprimer les anciennes colonnes de statut
            const existingStatusHeaders = headerRow.querySelectorAll('.status-header');
            existingStatusHeaders.forEach(header => header.remove());

            // Trouver le nombre maximum de colonnes parmi toutes les commandes
            const maxColonnes = Math.max(...commandes.map(cmd => cmd.nombreColonnes || 10), nombreColonnesActuel);

            // Ajouter les nouvelles colonnes de statut
            for (let i = 1; i <= maxColonnes; i++) {
                const th = document.createElement('th');
                th.className = 'status-col status-header';
                th.textContent = i.toString();
                headerRow.appendChild(th);
            }
        }

        function chargerDonnees() {
            // Créer les colonnes de statut d'abord
            creerColonnesStatut();

            const tbody = document.getElementById('corpsTable');
            tbody.innerHTML = '';

            // Trouver le nombre maximum de colonnes pour créer l'en-tête
            const maxColonnes = Math.max(...commandes.map(cmd => cmd.nombreColonnes || 10), nombreColonnesActuel);

            commandes.forEach(commande => {
                const tr = document.createElement('tr');
                tr.onclick = () => selectionnerLigne(tr, commande.id);

                // S'assurer que la commande a une propriété nombreColonnes
                if (!commande.nombreColonnes) {
                    commande.nombreColonnes = 10;
                }

                // Générer les checkboxes selon le nombre de colonnes de cette commande
                let statutsHtml = '';
                for (let i = 0; i < maxColonnes; i++) {
                    if (i < commande.nombreColonnes) {
                        const isChecked = i < commande.statuts.length ? commande.statuts[i] : 0;
                        statutsHtml += `<td><input type="checkbox" class="status-checkbox" ${isChecked ? 'checked' : ''} onchange="changerStatut(${commande.id}, ${i}, this.checked)"></td>`;
                    } else {
                        // Cellule vide pour les colonnes non utilisées
                        statutsHtml += `<td style="background-color: #f5f5f5;"></td>`;
                    }
                }

                // Calculer le restant pour cette commande
                const restant = commande.prix - commande.totalPaye;

                // Ajouter un contrôle pour changer le nombre de colonnes de cette commande
                const controlColonnes = `
                    <div style="display: flex; align-items: center; gap: 5px; font-size: 10px;">
                        <input type="number" value="${commande.nombreColonnes}" min="1" max="20"
                               style="width: 40px; padding: 2px; font-size: 10px;"
                               onchange="changerNombreColonnesCommande(${commande.id}, this.value)">
                        <span style="color: #666;">cols</span>
                    </div>
                `;

                tr.innerHTML = `
                    <td>${commande.id}<br>${controlColonnes}</td>
                    <td>${commande.refClient}</td>
                    <td>${commande.nomPrenom}</td>
                    <td>${commande.articles}</td>
                    <td>${commande.dateCommande}</td>
                    <td style="font-weight: bold;">${commande.prix.toFixed(2)}</td>
                    <td style="color: ${commande.totalPaye > 0 ? '#228b22' : '#666'}; font-weight: bold;">${commande.totalPaye.toFixed(2)}</td>
                    <td style="color: ${restant > 0 ? '#dc143c' : '#228b22'}; font-weight: bold;">${restant.toFixed(2)}</td>
                    ${statutsHtml}
                `;

                tbody.appendChild(tr);
            });

            // Mettre à jour les statistiques
            mettreAJourStatistiques();
        }

        function mettreAJourStatistiques() {
            const totalGeneral = commandes.reduce((sum, cmd) => sum + cmd.prix, 0);
            const totalPaye = commandes.reduce((sum, cmd) => sum + cmd.totalPaye, 0);
            const totalRestant = totalGeneral - totalPaye;

            document.getElementById('totalGeneral').textContent = `TOTAL GÉNÉRAL: ${totalGeneral.toFixed(2)} DA`;
            document.getElementById('totalPaye').textContent = `TOTAL PAYÉ: ${totalPaye.toFixed(2)} DA`;
            document.getElementById('totalRestant').textContent = `RESTANT: ${totalRestant.toFixed(2)} DA`;

            // Changer la couleur selon le statut
            const elementRestant = document.getElementById('totalRestant');
            if (totalRestant <= 0) {
                elementRestant.style.color = '#228b22'; // Vert si tout est payé
            } else {
                elementRestant.style.color = '#dc143c'; // Rouge s'il reste à payer
            }
        }

        function selectionnerLigne(tr, id) {
            // Désélectionner toutes les lignes
            document.querySelectorAll('tr').forEach(row => row.classList.remove('selected'));
            // Sélectionner la ligne cliquée
            tr.classList.add('selected');
            ligneSelectionnee = id;
        }

        function ouvrirModalAjout() {
            document.getElementById('modalAjout').style.display = 'block';
            // Définir la date d'aujourd'hui par défaut
            document.getElementById('dateCommande').value = new Date().toISOString().split('T')[0];
        }

        function fermerModal() {
            document.getElementById('modalAjout').style.display = 'none';
            // Réinitialiser le formulaire
            document.getElementById('refClient').value = '';
            document.getElementById('nomPrenom').value = '';
            document.getElementById('articles').value = '';
            document.getElementById('prix').value = '';
            document.getElementById('totalPaye').value = '';
        }

        function sauvegarderCommande() {
            const refClient = document.getElementById('refClient').value.trim();
            const nomPrenom = document.getElementById('nomPrenom').value.trim();
            const articles = document.getElementById('articles').value.trim();
            const dateCommande = document.getElementById('dateCommande').value;
            const prix = parseFloat(document.getElementById('prix').value) || 0;

            if (!refClient || !nomPrenom || !articles || prix <= 0) {
                alert('Veuillez remplir tous les champs obligatoires et saisir un prix valide.');
                return;
            }

            const nouvelleCommande = {
                id: prochainId++,
                refClient,
                nomPrenom,
                articles,
                dateCommande: new Date(dateCommande).toLocaleDateString('fr-FR'),
                prix,
                totalPaye: 0, // Sera calculé automatiquement selon les statuts
                statuts: [0,0,0,0,0,0,0,0,0,0],
                nombreColonnes: nombreColonnesActuel // استخدام العدد الافتراضي الحالي
            };

            commandes.unshift(nouvelleCommande);
            sauvegarderDonnees(); // Sauvegarder dans localStorage
            chargerDonnees();
            fermerModal();
            alert('Commande ajoutée avec succès! Le Total Payé sera calculé automatiquement selon les statuts cochés.');
        }

        function supprimerCommande() {
            if (!ligneSelectionnee) {
                alert('Veuillez sélectionner une commande à supprimer.');
                return;
            }

            if (confirm('Êtes-vous sûr de vouloir supprimer cette commande?')) {
                commandes = commandes.filter(c => c.id !== ligneSelectionnee);
                ligneSelectionnee = null;
                sauvegarderDonnees(); // Sauvegarder dans localStorage
                chargerDonnees();
                alert('Commande supprimée avec succès!');
            }
        }

        function actualiserDonnees() {
            chargerDonnees();
            alert('Données actualisées!');
        }

        function reinitialiserDonnees() {
            if (confirm('Êtes-vous sûr de vouloir réinitialiser toutes les données? Cette action est irréversible.')) {
                localStorage.removeItem('commandesData');
                commandes = chargerDonneesInitiales();
                chargerDonnees();
                alert('Données réinitialisées avec succès!');
            }
        }

        function changerStatut(id, index, estCoche) {
            const commande = commandes.find(c => c.id === id);
            if (commande) {
                // S'assurer que la commande a une propriété nombreColonnes
                if (!commande.nombreColonnes) {
                    commande.nombreColonnes = 10;
                }

                // S'assurer que le tableau des statuts a la bonne taille
                while (commande.statuts.length < commande.nombreColonnes) {
                    commande.statuts.push(0);
                }

                commande.statuts[index] = estCoche ? 1 : 0;

                // Calculer automatiquement TOTAL PAYÉ avec le nombre de colonnes de cette commande
                let nombreStatutsCoches = 0;
                for (let i = 0; i < commande.nombreColonnes; i++) {
                    if (i < commande.statuts.length && commande.statuts[i]) {
                        nombreStatutsCoches++;
                    }
                }
                commande.totalPaye = (nombreStatutsCoches * commande.prix) / commande.nombreColonnes;

                // Sauvegarder dans localStorage
                sauvegarderDonnees();

                // Recharger l'affichage pour mettre à jour TOTAL PAYÉ
                chargerDonnees();
            }
        }

        function changerNombreColonnesCommande(id, nouveauNombre) {
            const commande = commandes.find(c => c.id === id);
            const nombre = parseInt(nouveauNombre);

            if (!commande || isNaN(nombre) || nombre < 1 || nombre > 20) {
                alert('Veuillez saisir un nombre entre 1 et 20.');
                chargerDonnees(); // Recharger pour restaurer la valeur précédente
                return;
            }

            commande.nombreColonnes = nombre;

            // Ajuster le tableau des statuts
            while (commande.statuts.length < nombre) {
                commande.statuts.push(0);
            }

            // Recalculer le total payé avec le nouveau nombre de colonnes
            let nombreStatutsCoches = 0;
            for (let i = 0; i < nombre; i++) {
                if (i < commande.statuts.length && commande.statuts[i]) {
                    nombreStatutsCoches++;
                }
            }
            commande.totalPaye = (nombreStatutsCoches * commande.prix) / nombre;

            // Sauvegarder et recharger
            sauvegarderDonnees();
            chargerDonnees();

            console.log(`✅ تم تغيير عدد الأعمدة للطلب ${id} إلى ${nombre}`);
        }

        function sauvegarderDonnees() {
            const cleStockage = `commandesData_${groupeActuel}`;
            localStorage.setItem(cleStockage, JSON.stringify(commandes));

            // Mettre à jour les statistiques du groupe
            mettreAJourStatistiquesGroupe();
        }

        function mettreAJourTitreGroupe() {
            const groupes = JSON.parse(localStorage.getItem('groupes') || '[]');
            const groupe = groupes.find(g => g.id === groupeActuel);
            const nomGroupe = groupe ? groupe.nom : 'GROUPE 02';
            document.getElementById('titreGroupe').textContent = `GESTIONNAIRE DE COMMANDES CLIENTS - ${nomGroupe}`;
        }

        function mettreAJourStatistiquesGroupe() {
            const groupes = JSON.parse(localStorage.getItem('groupes') || '[]');
            const groupeIndex = groupes.findIndex(g => g.id === groupeActuel);

            if (groupeIndex !== -1) {
                groupes[groupeIndex].nombreClients = commandes.length;
                groupes[groupeIndex].chiffreAffaires = commandes.reduce((total, cmd) => total + (cmd.prix || 0), 0);
                localStorage.setItem('groupes', JSON.stringify(groupes));
            }
        }

        function retournerGroupes() {
            window.location.href = 'GestionnaireGroupes.html';
        }

        function appliquerNombreColonnes() {
            const nouveauNombre = parseInt(document.getElementById('nombreColonnes').value);

            if (isNaN(nouveauNombre) || nouveauNombre < 1 || nouveauNombre > 20) {
                alert('Veuillez saisir un nombre entre 1 et 20.');
                return;
            }

            nombreColonnesActuel = nouveauNombre;
            localStorage.setItem('nombreColonnes', nombreColonnesActuel.toString());

            // هذا سيكون العدد الافتراضي للطلبات الجديدة فقط
            alert(`تم تحديث العدد الافتراضي للأعمدة إلى: ${nombreColonnesActuel}\nسيتم تطبيقه على الطلبات الجديدة.\n\nلتغيير عدد الأعمدة لطلب موجود، استخدم المربع الصغير تحت رقم الطلب.`);

            // إعادة تحميل العرض لإظهار التغييرات
            chargerDonnees();
        }

        function exporterExcel() {
            try {
                // Créer les données pour Excel
                const donneesExcel = [];

                // En-tête principal
                donneesExcel.push(['GESTIONNAIRE DE COMMANDES CLIENTS - GROUPE 02']);
                donneesExcel.push([]);
                donneesExcel.push(['Date de génération:', new Date().toLocaleString('fr-FR')]);
                donneesExcel.push(['Nombre total de commandes:', commandes.length]);
                donneesExcel.push([]);

                // En-têtes des colonnes
                const entetes = ['N°', 'REF CLI', 'NOM ET PRÉNOM', 'ARTICLES COM', 'DATE DE COM', 'PRIX', 'TOTAL PAYÉ', 'RESTANT', 'NB COLS'];

                // Trouver le nombre maximum de colonnes
                const maxColonnes = Math.max(...commandes.map(cmd => cmd.nombreColonnes || 10));

                // Ajouter les colonnes de statut selon le nombre maximum
                for (let i = 1; i <= maxColonnes; i++) {
                    entetes.push(i.toString());
                }
                donneesExcel.push(entetes);

                // Données des commandes
                commandes.forEach(commande => {
                    const ligne = [
                        commande.id,
                        commande.refClient,
                        commande.nomPrenom,
                        commande.articles,
                        commande.dateCommande,
                        commande.prix,
                        commande.totalPaye,
                        commande.prix - commande.totalPaye,
                        commande.nombreColonnes || 10
                    ];

                    // Ajouter les statuts selon le nombre maximum de colonnes
                    for (let i = 0; i < maxColonnes; i++) {
                        if (i < (commande.nombreColonnes || 10)) {
                            const statut = i < commande.statuts.length ? commande.statuts[i] : 0;
                            ligne.push(statut ? '✓' : '');
                        } else {
                            ligne.push(''); // Cellule vide pour les colonnes non utilisées
                        }
                    }

                    donneesExcel.push(ligne);
                });

                // Ligne vide avant les totaux
                donneesExcel.push([]);

                // Calcul des totaux
                const totalGeneral = commandes.reduce((sum, cmd) => sum + cmd.prix, 0);
                const totalPaye = commandes.reduce((sum, cmd) => sum + cmd.totalPaye, 0);
                const totalRestant = totalGeneral - totalPaye;

                // Ligne des totaux
                const ligneTotaux = ['TOTAUX:', '', '', '', '', totalGeneral, totalPaye, totalRestant, ''];
                for (let i = 0; i < maxColonnes; i++) {
                    ligneTotaux.push('');
                }
                donneesExcel.push(ligneTotaux);

                // Créer le classeur Excel
                const wb = XLSX.utils.book_new();
                const ws = XLSX.utils.aoa_to_sheet(donneesExcel);

                // Styles et formatage
                const range = XLSX.utils.decode_range(ws['!ref']);

                // Fusionner la cellule du titre
                if (!ws['!merges']) ws['!merges'] = [];
                ws['!merges'].push({
                    s: { r: 0, c: 0 },
                    e: { r: 0, c: entetes.length - 1 }
                });

                // Ajuster la largeur des colonnes
                const colWidths = [
                    { wch: 5 },   // N°
                    { wch: 12 },  // REF CLI
                    { wch: 25 },  // NOM ET PRÉNOM
                    { wch: 30 },  // ARTICLES COM
                    { wch: 12 },  // DATE DE COM
                    { wch: 12 },  // PRIX
                    { wch: 12 },  // TOTAL PAYÉ
                    { wch: 12 },  // RESTANT
                    { wch: 8 }    // NB COLS
                ];

                // Ajouter largeur pour colonnes de statut
                for (let i = 0; i < maxColonnes; i++) {
                    colWidths.push({ wch: 5 });
                }

                ws['!cols'] = colWidths;

                // Ajouter la feuille au classeur
                XLSX.utils.book_append_sheet(wb, ws, 'Facture Commandes');

                // Générer le nom du fichier
                const maintenant = new Date();
                const nomFichier = `Facture_Commandes_${maintenant.getFullYear()}-${String(maintenant.getMonth() + 1).padStart(2, '0')}-${String(maintenant.getDate()).padStart(2, '0')}_${String(maintenant.getHours()).padStart(2, '0')}-${String(maintenant.getMinutes()).padStart(2, '0')}.xlsx`;

                // Télécharger le fichier
                XLSX.writeFile(wb, nomFichier);

                alert('Facture Excel générée et téléchargée avec succès!');

            } catch (error) {
                console.error('Erreur lors de l\'exportation Excel:', error);
                alert('Erreur lors de la génération du fichier Excel: ' + error.message);
            }
        }

        function ouvrirFacture() {
            // Sauvegarder les données actuelles
            sauvegarderDonnees();

            // Ouvrir الفاتورة المخصصة لـ GROUPE 02 في تبويب جديد
            window.open('FactureTemplateGROUPE 02.html', '_blank');
        }

        function changerLogoFacture() {
            document.getElementById('logoFactureUpload').click();
        }

        function traiterLogoFacture(input) {
            if (input.files && input.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    // Sauvegarder l'image dans localStorage pour la facture
                    localStorage.setItem('logoFacture', e.target.result);

                    // Afficher un message de confirmation
                    alert('Logo de facture mis à jour avec succès!\nLe nouveau logo apparaîtra dans les prochaines factures générées.');
                };
                reader.readAsDataURL(input.files[0]);
            }
        }

        // Fermer le modal en cliquant à l'extérieur
        window.onclick = function(event) {
            const modal = document.getElementById('modalAjout');
            if (event.target === modal) {
                fermerModal();
            }
        }

        // Charger les données au démarrage
        document.addEventListener('DOMContentLoaded', function() {
            // تعيين المجموعة الحالية إذا لم تكن موجودة
            if (!localStorage.getItem('groupeActuel')) {
                localStorage.setItem('groupeActuel', 'groupe-02');
            }

            // التأكد من أن المجموعة الحالية هي groupe-02
            groupeActuel = 'groupe-02';
            localStorage.setItem('groupeActuel', groupeActuel);

            // تحديث قيمة عدد الأعمدة في الواجهة
            document.getElementById('nombreColonnes').value = nombreColonnesActuel;

            // إعادة حساب جميع القيم عند التحميل باستخدام العدد المحفوظ
            commandes.forEach(commande => {
                // S'assurer que le tableau des statuts a la bonne taille
                while (commande.statuts.length < nombreColonnesActuel) {
                    commande.statuts.push(0);
                }

                // Ne compter que les statuts dans la plage des colonnes actuelles
                let nombreStatutsCoches = 0;
                for (let i = 0; i < nombreColonnesActuel; i++) {
                    if (i < commande.statuts.length && commande.statuts[i]) {
                        nombreStatutsCoches++;
                    }
                }
                commande.totalPaye = (nombreStatutsCoches * commande.prix) / nombreColonnesActuel;
            });

            // حفظ البيانات المحدثة
            sauvegarderDonnees();

            // تحميل العرض
            chargerDonnees();
        });
    </script>
</body>
</html>
